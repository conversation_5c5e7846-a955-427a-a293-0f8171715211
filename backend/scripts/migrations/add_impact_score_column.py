#!/usr/bin/env python3
# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import sqlite3
import logging
import os
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def add_impact_score_column():
    """向news_impact_analysis表添加impact_score列（如果不存在）"""
    
    # 数据库路径
    db_path = "data/news_impact_analysis.db"
    
    # 确保数据库文件存在
    if not os.path.exists(db_path):
        logger.error(f"数据库文件不存在: {db_path}")
        return False
    
    try:
        # 连接到数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='news_impact_analysis'
        """)
        
        if not cursor.fetchone():
            logger.error("news_impact_analysis表不存在")
            conn.close()
            return False
        
        # 检查impact_score列是否已存在
        cursor.execute("PRAGMA table_info(news_impact_analysis)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        if 'impact_score' in column_names:
            logger.info("impact_score列已存在，无需添加")
            conn.close()
            return True
        
        # 添加impact_score列
        logger.info("正在添加impact_score列...")
        cursor.execute("""
            ALTER TABLE news_impact_analysis 
            ADD COLUMN impact_score REAL DEFAULT 50.0
        """)
        
        # 提交更改
        conn.commit()
        logger.info("impact_score列添加成功")
        
        # 验证列是否添加成功
        cursor.execute("PRAGMA table_info(news_impact_analysis)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        if 'impact_score' in column_names:
            logger.info("验证成功：impact_score列已存在于表中")
            success = True
        else:
            logger.error("验证失败：impact_score列未能添加到表中")
            success = False
        
        conn.close()
        return success
        
    except Exception as e:
        logger.error(f"添加impact_score列失败: {e}")
        if 'conn' in locals():
            conn.close()
        return False

if __name__ == "__main__":
    logger.info("开始执行数据库迁移：添加impact_score列")
    success = add_impact_score_column()
    
    if success:
        logger.info("数据库迁移完成")
        exit(0)
    else:
        logger.error("数据库迁移失败")
        exit(1) 