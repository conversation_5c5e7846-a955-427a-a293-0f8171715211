#!/usr/bin/env python3
"""
LLM管理器
处理与大语言模型的交互，支持Google Gemini API (OpenAI兼容)
"""

import os
import logging
import json
from datetime import datetime
from typing import Dict, List, Any, Optional, AsyncGenerator
import asyncio

logger = logging.getLogger(__name__)

# 导入OpenAI客户端和模拟LLM管理器
try:
    from openai import OpenAI, AsyncOpenAI
except ImportError:
    logger.error("OpenAI库未安装，请运行: pip install openai")
    OpenAI = None
    AsyncOpenAI = None

try:
    from .mock_llm import MockLLMManager
except ImportError:
    from mock_llm import MockLLMManager

class LLMManager:
    """LLM管理器 - 使用Gemini的OpenAI兼容API，简化调用逻辑"""
    
    def __init__(self):
        self.client = None
        self.async_client = None
        self.model = None
        self.mock_mode = False
        self.mock_llm = None
        self._initialize_client()
    
    def _initialize_client(self):
        """初始化Gemini OpenAI兼容客户端"""
        try:
            # 检查OpenAI库是否可用
            if OpenAI is None or AsyncOpenAI is None:
                logger.warning("OpenAI库不可用，启用模拟模式")
                self._enable_mock_mode()
                return
            
            # 从环境变量读取配置
            api_key = os.getenv('GEMINI_API_KEY')
            model = os.getenv('GEMINI_MODEL', 'gemini-2.0-flash')
            base_url = "https://generativelanguage.googleapis.com/v1beta/openai/"
            
            if not api_key:
                logger.warning("GEMINI_API_KEY环境变量未设置，启用模拟模式")
                self._enable_mock_mode()
                return
            
            # 创建OpenAI兼容客户端
            self.client = OpenAI(
                api_key=api_key,
                base_url=base_url
            )
            
            self.async_client = AsyncOpenAI(
                api_key=api_key,
                base_url=base_url
            )
            
            self.model = model
            
            logger.info(f"Gemini OpenAI兼容客户端初始化成功，使用模型: {model}")
            
        except Exception as e:
            logger.error(f"Gemini客户端初始化失败: {e}")
            logger.warning("启用模拟模式作为备用方案")
            self._enable_mock_mode()
    
    def _enable_mock_mode(self):
        """启用模拟模式"""
        self.mock_mode = True
        self.mock_llm = MockLLMManager()
        logger.info("已启用模拟LLM模式")

    async def get_completion(self, prompt: str, system_message: Optional[str] = None,
                           temperature: float = 0.7, max_tokens: int = 2000) -> str:
        """
        获取LLM完成响应
        
        Args:
            prompt: 用户提示词
            system_message: 系统消息
            temperature: 温度参数
            max_tokens: 最大token数
            
        Returns:
            str: LLM响应内容
        """
        
        # 如果是模拟模式，使用模拟LLM
        if self.mock_mode:
            return await self.mock_llm.get_completion(prompt, system_message, temperature, max_tokens)
        
        if not self.async_client:
            logger.warning("Gemini客户端不可用，回退到模拟模式")
            self._enable_mock_mode()
            return await self.mock_llm.get_completion(prompt, system_message, temperature, max_tokens)
        
        try:
            # 构建消息列表
            messages = []
            
            if system_message:
                messages.append({
                    "role": "system", 
                    "content": system_message
                })
            
            messages.append({
                "role": "user",
                "content": prompt
            })
            
            logger.info(f"正在调用Gemini API (OpenAI兼容): {self.model}")
            
            # 使用OpenAI兼容API调用
            response = await self.async_client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            logger.info("Gemini API调用成功")
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"Gemini API调用错误: {e}")
            logger.warning("Gemini API调用失败，回退到模拟模式")
            self._enable_mock_mode()
            return await self.mock_llm.get_completion(prompt, system_message, temperature, max_tokens)
    
    async def get_streaming_completion(self, prompt: str, system_message: Optional[str] = None,
                                     temperature: float = 0.7, 
                                     max_tokens: int = 2000) -> AsyncGenerator[str, None]:
        """
        获取流式LLM响应
        
        Args:
            prompt: 用户提示词
            system_message: 系统消息
            temperature: 温度参数
            max_tokens: 最大token数
            
        Yields:
            str: 流式响应内容片段
        """
        
        # 如果是模拟模式，使用模拟LLM
        if self.mock_mode:
            async for chunk in self.mock_llm.get_streaming_completion(prompt, system_message, temperature, max_tokens):
                yield chunk
            return
        
        if not self.async_client:
            logger.warning("Gemini客户端不可用，回退到模拟模式")
            self._enable_mock_mode()
            async for chunk in self.mock_llm.get_streaming_completion(prompt, system_message, temperature, max_tokens):
                yield chunk
            return
        
        try:
            # 构建消息列表
            messages = []
            
            if system_message:
                messages.append({
                    "role": "system",
                    "content": system_message
                })
            
            messages.append({
                "role": "user",
                "content": prompt
            })
            
            logger.info(f"正在调用Gemini流式API (OpenAI兼容): {self.model}")
            
            # 使用OpenAI兼容流式API调用
            stream = await self.async_client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                stream=True
            )
            
            logger.info("Gemini流式API调用成功")
            
            async for chunk in stream:
                if chunk.choices[0].delta.content is not None:
                    yield chunk.choices[0].delta.content
                    
        except Exception as e:
            logger.error(f"Gemini流式调用错误: {e}")
            logger.warning("Gemini流式调用失败，回退到模拟模式")
            self._enable_mock_mode()
            async for chunk in self.mock_llm.get_streaming_completion(prompt, system_message, temperature, max_tokens):
                yield chunk
    
    async def analyze_user_intent(self, user_input: str) -> Dict[str, Any]:
        """
        分析用户意图
        
        Args:
            user_input: 用户输入
            
        Returns:
            Dict: 分析结果
        """
        
        system_message = """
        你是一个专业的金融分析助手。请分析用户的输入，判断用户想要进行什么类型的分析。

        可能的分析类型包括：
        1. 技术分析 - 用户想了解股票的技术指标、图表形态等
        2. 基本面分析 - 用户想了解公司的财务状况、估值等
        3. 行业分析 - 用户想了解某个行业的情况
        4. 市场分析 - 用户想了解整体市场情况
        5. 个股查询 - 用户想了解某只股票的基本信息
        6. 投资建议 - 用户寻求投资建议
        7. 风险评估 - 用户想了解投资风险
        8. 其他

        请以JSON格式返回分析结果，包含：
        - analysis_type: 分析类型
        - confidence: 置信度(0-1)
        - extracted_symbols: 提取到的股票代码列表
        - keywords: 关键词列表
        - user_intent: 用户意图描述
        """
        
        prompt = f"请分析以下用户输入：\n\n{user_input}"
        
        try:
            # 如果是模拟模式，直接使用模拟分析
            if self.mock_mode:
                return await self.mock_llm.analyze_user_intent(user_input)
            
            response = await self.get_completion(prompt, system_message)
            
            # 尝试解析JSON响应
            try:
                result = json.loads(response)
                return result
            except json.JSONDecodeError:
                # 如果无法解析JSON，返回默认结果
                return {
                    "analysis_type": "其他",
                    "confidence": 0.5,
                    "extracted_symbols": [],
                    "keywords": [],
                    "user_intent": response
                }
                
        except Exception as e:
            logger.error(f"用户意图分析错误: {e}")
            return {
                "analysis_type": "其他",
                "confidence": 0.0,
                "extracted_symbols": [],
                "keywords": [],
                "user_intent": "分析失败",
                "error": str(e)
            }
    
    async def generate_report(self, analysis_data: Dict[str, Any], 
                            report_type: str = "comprehensive") -> str:
        """
        生成分析报告
        
        Args:
            analysis_data: 分析数据
            report_type: 报告类型
            
        Returns:
            str: 生成的报告
        """
        
        system_message = f"""
        你是一个专业的金融分析报告撰写专家。请基于提供的分析数据生成一份{report_type}分析报告。

        报告要求：
        1. 结构清晰，逻辑严谨
        2. 数据准确，分析客观
        3. 语言专业，易于理解
        4. 包含具体的投资建议
        5. 提及相关风险

        报告格式：
        # 投资分析报告

        ## 执行摘要
        
        ## 详细分析
        
        ## 投资建议
        
        ## 风险提示
        
        ## 免责声明
        """
        
        prompt = f"""
        请基于以下分析数据生成投资分析报告：

        {json.dumps(analysis_data, ensure_ascii=False, indent=2)}
        """
        
        try:
            # 如果是模拟模式，直接使用模拟报告生成
            if self.mock_mode:
                return await self.mock_llm.generate_report(analysis_data, report_type)
            
            report = await self.get_completion(prompt, system_message, temperature=0.3)
            return report
            
        except Exception as e:
            logger.error(f"报告生成错误: {e}")
            return f"报告生成失败: {str(e)}"
    
    async def analyze_news_impact_with_gemini(self, news_title: str, news_content: str) -> Dict[str, Any]:
        """
        使用Gemini分析新闻对金融市场的影响
        
        Args:
            news_title: 新闻标题
            news_content: 新闻内容
            
        Returns:
            分析结果字典
        """
        try:
            # 构建分析提示词，与GLM客户端保持一致的格式
            prompt = f"""
你是一位专业的金融分析师，请分析以下新闻对金融市场的潜在影响。

请参考以下分析示例，学习如何从新闻事件推导到相关股票的专业逻辑：

【示例1：供应链紧缺事件】
事件描述：Asahi旭化成向部分客户发出供应调整通知，因AI算力需求激增导致PSPI需求爆发，现有产能无法匹配市场扩张节奏，宣布将收紧其PIMEL系列感光材料的供应。
影响逻辑：感光材料供应紧缺→下游封测厂商寻找替代供应商→国内同类产品厂商受益于进口替代和供需缺口
相关股票：阳谷华泰（感光材料生产商，直接受益于供应缺口）
推荐理由：公司是国内少数具备PSPI感光材料生产能力的企业，在上游供应紧张时期将获得更多市场份额和定价权

【示例2：地缘政治事件】
事件描述：以色列对伊朗进行空袭，打击军事基地及核设施
影响逻辑：地缘冲突升级→军工需求增加+石油供应担忧→相关防务和能源股受益
相关股票：科力股份（军工设备）、洲际油气（石油开采）、金牛化工（军工材料）、金瑞矿业（军工金属材料）
推荐理由：地缘紧张推动军费支出和防务需求，同时中东冲突影响油价，相关产业链公司将从需求增长和价格上涨中受益

现在请分析以下新闻，参考上述示例的分析逻辑，重点关注事件与股票之间的因果关系：

新闻标题：{news_title}
新闻内容：{news_content}

请从以下维度进行分析，并以JSON格式返回结果：

{{
    "overall_impact": {{
        "level": "高/中/低",
        "summary": "整体影响概述"
    }},
    "us_market": {{
        "impact_level": "高/中/低",
        "direction": "利多/利空/中性",
        "affected_sectors": ["相关行业1", "相关行业2"],
        "recommended_stocks": [
            {{
                "symbol": "股票代码1",
                "name": "公司名称1", 
                "impact": "利多/利空",
                "reason": "详细的因果关系分析"
            }}
        ],
        "analysis": "详细分析"
    }},
    "a_share_market": {{
        "impact_level": "高/中/低",
        "direction": "利多/利空/中性",
        "affected_sectors": ["相关行业1", "相关行业2"],
        "recommended_stocks": [
            {{
                "symbol": "股票代码1",
                "name": "公司名称1",
                "impact": "利多/利空", 
                "reason": "详细的因果关系分析"
            }}
        ],
        "analysis": "详细分析"
    }},
    "hk_market": {{
        "impact_level": "高/中/低",
        "direction": "利多/利空/中性",
        "affected_sectors": ["相关行业1", "相关行业2"],
        "recommended_stocks": [
            {{
                "symbol": "股票代码1",
                "name": "公司名称1",
                "impact": "利多/利空",
                "reason": "详细的因果关系分析"
            }}
        ],
        "analysis": "详细分析"
    }},
    "major_indices": {{
        "sp500": {{"impact": "利多/利空/中性", "reason": "原因说明"}},
        "nasdaq": {{"impact": "利多/利空/中性", "reason": "原因说明"}},
        "shanghai_composite": {{"impact": "利多/利空/中性", "reason": "原因说明"}},
        "shenzhen_component": {{"impact": "利多/利空/中性", "reason": "原因说明"}},
        "hang_seng": {{"impact": "利多/利空/中性", "reason": "原因说明"}}
    }},
    "key_stocks": [
        {{
            "symbol": "股票代码",
            "name": "公司名称", 
            "market": "US/A股/港股",
            "impact": "利多/利空",
            "impact_level": "高/中/低",
            "reason": "详细原因"
        }}
    ],
    "risk_assessment": {{
        "short_term_risk": "高/中/低",
        "medium_term_risk": "高/中/低", 
        "key_risk_factors": ["风险因素1", "风险因素2"]
    }},
    "investment_advice": {{
        "strategy": "投资策略建议",
        "attention_points": ["注意点1", "注意点2"],
        "time_horizon": "短期/中期/长期"
    }}
}}

请确保返回的是严格的JSON格式，不要包含任何其他文字。
"""

            # 调用Gemini API
            response = await self.get_completion(prompt, temperature=0.3, max_tokens=3000)
            
            # 解析JSON响应，处理代码块包装的情况
            try:
                # 移除可能的代码块标记
                clean_response = response.strip()
                if clean_response.startswith('```json'):
                    clean_response = clean_response[7:]  # 移除 ```json
                if clean_response.endswith('```'):
                    clean_response = clean_response[:-3]  # 移除 ```
                clean_response = clean_response.strip()
                
                analysis_result = json.loads(clean_response)
                return {
                    'success': True,
                    'analysis': analysis_result,
                    'timestamp': datetime.now().isoformat()
                }
            except json.JSONDecodeError as e:
                logger.error(f"Gemini返回的JSON格式无效: {e}")
                logger.error(f"原始响应: {response}")
                return {
                    'success': False,
                    'error': f'JSON解析失败: {str(e)}'
                }
                
        except Exception as e:
            logger.error(f"Gemini新闻影响分析失败: {e}")
            return {
                'success': False,
                'error': f'分析过程中发生错误: {str(e)}'
            }

    def is_available(self) -> bool:
        """检查LLM是否可用"""
        return (self.client is not None and self.async_client is not None) or self.mock_mode 