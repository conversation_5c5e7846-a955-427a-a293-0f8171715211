#!/usr/bin/env python3
"""
DeerFlow 后端 API 服务器
支持流式输出的多智能体系统接口
"""

import asyncio
import json
import logging
import os
import sys
from typing import AsyncGenerator, List, Optional, Any, Dict

import uvicorn
from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
import pandas as pd
import numpy as np
import datetime

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 导入基础模块
from backend.core.analysis.factors.basic_factors import FactorCalculator, get_all_factors
from backend.core.analysis.ml.models import MLModelManager, ModelPredictor
from backend.core.analysis.scoring.scoring_system import Factor<PERSON>corer, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, get_stock_ranker
from backend.core.data.sources.data_sources import get_data_source_manager, get_alternative_data_source
from backend.core.analysis.backtesting.backtesting import get_backtest_engine, simple_momentum_strategy, factor_score_strategy
from backend.core.analysis.risk.risk_management import get_risk_analyzer, get_risk_monitor
from backend.apis.data_api import router as data_router, get_data_manager_dependency
from backend.core.data.managers.data_manager import init_data_manager
from backend.apis.factor_api import factor_router
from backend.apis.metadata_api import router as metadata_router
from backend.ai.tools.cache_manager import cache_manager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# 导入集成的AI模块
try:
    from backend.ai import AIWorkflowManager
    from backend.ai.graph import build_graph
    AI_MODULE_AVAILABLE = True
    logger.info("AI模块导入成功")
except ImportError as e:
    logger.warning(f"AI模块导入失败: {e}, 将使用传统模式")
    AIWorkflowManager = None
    build_graph = None
    AI_MODULE_AVAILABLE = False

# 创建 FastAPI 应用
app = FastAPI(
    title="金融投资助手 API",
    description="智能投资分析平台 API 服务器",
    version="1.0.0"
)

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000", "http://127.0.0.1:3000",
        "http://localhost:3001", "http://127.0.0.1:3001",
        "http://localhost:3002", "http://127.0.0.1:3002",
        "http://localhost:3003", "http://127.0.0.1:3003",
        "http://localhost:3004", "http://127.0.0.1:3004",  # 添加3004端口支持
        "file://"  # 支持本地文件访问（用于测试页面）
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册数据管理路由
app.include_router(data_router)

# 注册增强型因子计算路由
app.include_router(factor_router)

# 注册股票元数据管理路由
app.include_router(metadata_router)

# 注册用户认证路由
try:
    from backend.auth import auth_router
    from backend.auth.auth_dependencies import get_current_active_user
    app.include_router(auth_router)
    logger.info("用户认证系统已启用")
except ImportError as e:
    logger.warning(f"用户认证系统导入失败: {e}")
except Exception as e:
    logger.error(f"用户认证系统初始化失败: {e}")

# 应用启动和关闭事件处理器
@app.on_event("startup")
async def startup_event():
    """应用启动时的初始化操作"""
    try:
        logger.info("正在启动AI金融分析系统...")

        # 启动新闻同步调度器
        try:
            from backend.services.tasks.news_sync_scheduler import start_news_scheduler
            await start_news_scheduler()
            logger.info("新闻同步调度器启动成功")
        except Exception as e:
            logger.warning(f"新闻同步调度器启动失败: {e}")

        # 启动Supabase同步服务
        try:
            from backend.services.tasks.local_to_supabase_sync import get_supabase_sync_service
            supabase_sync = get_supabase_sync_service()
            await supabase_sync.start_scheduler()
            logger.info("Supabase同步服务启动成功")
        except Exception as e:
            logger.warning(f"Supabase同步服务启动失败: {e}")

        logger.info("AI金融分析系统启动完成")

    except Exception as e:
        logger.error(f"应用启动失败: {e}")
        # 不抛出异常，允许应用继续启动


@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时的清理操作"""
    try:
        logger.info("正在关闭AI金融分析系统...")

        # 停止新闻同步调度器
        try:
            from backend.services.tasks.news_sync_scheduler import stop_news_scheduler
            await stop_news_scheduler()
            logger.info("新闻同步调度器已停止")
        except Exception as e:
            logger.warning(f"停止新闻同步调度器失败: {e}")

        # 停止Supabase同步服务
        try:
            from backend.services.tasks.local_to_supabase_sync import get_supabase_sync_service
            supabase_sync = get_supabase_sync_service()
            await supabase_sync.stop_scheduler()
            logger.info("Supabase同步服务已停止")
        except Exception as e:
            logger.warning(f"停止Supabase同步服务失败: {e}")

        logger.info("AI金融分析系统已关闭")

    except Exception as e:
        logger.error(f"应用关闭时出错: {e}")

# 请求模型
class ChatRequest(BaseModel):
    message: str
    max_plan_iterations: int = 1
    max_step_num: int = 3
    enable_background_investigation: bool = True
    debug: bool = False

class FactorRequest(BaseModel):
    symbol: str
    tushare_token: Optional[str] = None
    start_date: Optional[str] = None # YYYYMMDD 格式
    end_date: Optional[str] = None # YYYYMMDD 格式
    factors: Optional[List[str]] = None

class MLTrainRequest(BaseModel):
    symbols: List[str]
    model_id: str = 'random_forest'
    test_size: float = 0.2

class MLPredictRequest(BaseModel):
    symbols: List[str]
    model_id: str = 'random_forest'

class StockScoreRequest(BaseModel):
    symbol: str
    tushare_token: Optional[str] = None

class StockRankingRequest(BaseModel):
    symbols: List[str]
    max_stocks: int = 20
    factor_weight: float = 0.6
    ml_weight: float = 0.4
    tushare_token: Optional[str] = None

# AI因子生成相关模型
class AIFactorRequest(BaseModel):
    description: str  # 用户的自然语言描述

class DivergenceScanRequest(BaseModel):
    market: str  # US, HK, CN
    tushare_token: Optional[str] = None
    divergence_types: Optional[List[str]] = None  # ['bullish', 'bearish']

class StockDataQueryRequest(BaseModel):
    symbol: str
    tushare_token: Optional[str] = None
    period: Optional[str] = "1y"  # 1m, 3m, 6m, 1y, 2y, 5y
    start_date: Optional[str] = None
    end_date: Optional[str] = None

class TechnicalIndicatorRequest(BaseModel):
    symbol: str
    tushare_token: Optional[str] = None
    indicators: List[str] = ["sma", "ema", "rsi", "macd", "bollinger"]
    period: Optional[str] = "1y"

@app.get("/")
async def root():
    """健康检查接口"""
    return {"message": "金融投资助手 API 服务器运行中", "status": "healthy"}


@app.get("/health")
async def health_check():
    """详细健康检查"""
    return {
        "status": "healthy",
        "service": "金融投资助手智能分析系统",
        "version": "1.0.0"
    }

@app.get("/debug/frontend-health")
async def debug_frontend_health():
    """Debug endpoint for frontend connectivity testing"""
    import datetime
    return {
        "status": "healthy",
        "service": "金融投资助手智能分析系统",
        "version": "1.0.0",
        "timestamp": datetime.datetime.now().isoformat(),
        "cors_headers": {
            "access_control_allow_origin": "*",
            "access_control_allow_methods": "*",
            "access_control_allow_headers": "*"
        },
        "environment": "development",
        "endpoints_available": [
            "/health",
            "/factors",
            "/factors/list",
            "/factors/health", 
            "/divergence/markets",
            "/chat/stream",
            "/debug/frontend-health"
        ]
    }

async def stream_workflow_response(
    user_input: str,
    max_plan_iterations: int = 1,
    max_step_num: int = 3,
    enable_background_investigation: bool = True,
    debug: bool = False
) -> AsyncGenerator[str, None]:
    """
    流式执行工作流并返回结果
    """
    try:
        # 检查AI模块是否可用
        if not AI_MODULE_AVAILABLE or build_graph is None:
            error_data = {
                "type": "error",
                "content": "AI模块未正确安装或配置，请检查依赖项",
                "timestamp": asyncio.get_event_loop().time()
            }
            yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"
            return
        
        # 创建工作流图
        graph = build_graph()
        
        # 初始状态 - 使用LangChain消息格式
        from langchain_core.messages import HumanMessage
        initial_state = {
            "messages": [HumanMessage(content=user_input)],
            "auto_accepted_plan": True,
            "enable_background_investigation": enable_background_investigation,
        }
        
        # 配置
        config = {
            "configurable": {
                "thread_id": f"chat_{asyncio.current_task().get_name()}",
                "max_plan_iterations": max_plan_iterations,
                "max_step_num": max_step_num,
                "mcp_settings": {
                    "servers": {}
                },
            },
            "recursion_limit": 100,
        }
        
        # 流式处理
        last_message_cnt = 0
        final_report_sent = False
        
        async for state in graph.astream(
            input=initial_state, 
            config=config, 
            stream_mode="values"
        ):
            # 检查是否有最终报告（优先处理，避免重复）
            if isinstance(state, dict) and "final_report" in state and state["final_report"] and not final_report_sent:
                final_data = {
                    "type": "final_report",
                    "content": state["final_report"],
                    "timestamp": asyncio.get_event_loop().time()
                }
                yield f"data: {json.dumps(final_data, ensure_ascii=False)}\n\n"
                final_report_sent = True
                break
            
            # 只有在没有final_report的情况下才处理messages
            if not final_report_sent and isinstance(state, dict) and "messages" in state:
                if len(state["messages"]) <= last_message_cnt:
                    continue
                    
                last_message_cnt = len(state["messages"])
                message = state["messages"][-1]
                
                # 跳过用户自己的消息
                if hasattr(message, 'content') and message.content == user_input:
                    continue
                
                # 构造流式数据
                stream_data = {
                    "type": "message",
                    "content": str(message.content) if hasattr(message, 'content') else str(message),
                    "agent": getattr(message, 'name', 'system'),
                    "timestamp": asyncio.get_event_loop().time()
                }
                
                yield f"data: {json.dumps(stream_data, ensure_ascii=False)}\n\n"
        
        # 发送结束信号
        end_data = {
            "type": "end",
            "message": "工作流执行完成",
            "timestamp": asyncio.get_event_loop().time()
        }
        yield f"data: {json.dumps(end_data, ensure_ascii=False)}\n\n"
        
    except Exception as e:
        logger.error(f"工作流执行错误: {e}")
        error_data = {
            "type": "error",
            "message": f"执行错误: {str(e)}",
            "timestamp": asyncio.get_event_loop().time()
        }
        yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"


async def stream_ai_workflow_response(
    user_input: str,
    data_manager: Any,
    user_id: Optional[int] = None,
    max_plan_iterations: int = 1,
    max_step_num: int = 3,
    enable_background_investigation: bool = True,
    debug: bool = False
) -> AsyncGenerator[str, None]:
    """
    流式AI工作流响应生成器
    使用集成的AI智能体系统
    """
    logger.info(f"开始执行AI工作流: {user_input[:100]}...")
    
    try:
        # 创建用户感知的工作流管理器
        try:
            from backend.ai.user_aware_workflow import UserAwareAIWorkflowManager
            workflow_manager = UserAwareAIWorkflowManager(data_manager=data_manager)
        except ImportError:
            # 回退到普通工作流管理器
            workflow_manager = AIWorkflowManager(data_manager=data_manager)
        
        # Track whether chart data has been sent
        chart_data_sent = False
        
        # 执行工作流并流式返回结果
        async for result in workflow_manager.process_user_query(
            user_input,
            user_id=user_id,
            debug=debug,
            max_plan_iterations=max_plan_iterations,
            max_step_num=max_step_num,
            enable_background_investigation=enable_background_investigation
        ):
            # Handle chart data specially
            if result.get("type") == "chart_data" and not chart_data_sent:
                try:
                    # Extract chart config and ensure it's JSON serializable
                    chart_config = result.get("content", {})
                    chart_data = result.get("chart_data", {})
                    
                    # Create a safe copy without any function references
                    safe_chart_config = json.loads(json.dumps(chart_config, default=str))
                    
                    stream_data = {
                        "type": "chart_data",
                        "content": safe_chart_config,
                        "chart_data": chart_data,
                        "has_chart": result.get("has_chart", False),
                        "agent": result.get("agent", "chart_generator"),
                        "timestamp": result.get("timestamp", asyncio.get_event_loop().time())
                    }
                    chart_data_sent = True  # Mark as sent to prevent duplicates
                except Exception as e:
                    logger.error(f"处理图表数据时发生错误: {e}")
                    # Fall back to a simple chart indication
                    stream_data = {
                        "type": "chart_data",
                        "content": {"error": f"图表配置序列化失败: {str(e)}"},
                        "chart_data": result.get("chart_data", {}),
                        "has_chart": result.get("has_chart", False),
                        "agent": result.get("agent", "chart_generator"),
                        "timestamp": result.get("timestamp", asyncio.get_event_loop().time())
                    }
                    chart_data_sent = True  # Mark as sent even for errors
            elif result.get("type") == "chart_data" and chart_data_sent:
                # Skip duplicate chart data
                logger.debug("跳过重复的图表数据")
                continue
            else:
                # Handle normal message data
                stream_data = {
                    "type": result.get("type", "message"),
                    "content": result.get("content", ""),
                    "agent": result.get("agent", "ai_system"),
                    "timestamp": result.get("timestamp", asyncio.get_event_loop().time())
                }
            
            yield f"data: {json.dumps(stream_data, ensure_ascii=False)}\n\n"
        
        # 发送完成信号
        yield f"data: {json.dumps({'type': 'done'}, ensure_ascii=False)}\n\n"
        
    except Exception as e:
        logger.error(f"AI工作流执行错误: {e}")
        error_data = {
            "type": "error",
            "content": f"AI分析过程中发生错误: {str(e)}",
            "timestamp": asyncio.get_event_loop().time()
        }
        yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"


def get_default_data_manager():
    """获取默认的DataManager实例"""
    try:
        # 使用环境变量中的Token，如果没有则返回None
        token = os.getenv('TUSHARE_TOKEN')
        if token:
            return init_data_manager(token)
        else:
            logger.warning("TUSHARE_TOKEN环境变量未设置，使用空的DataManager")
            return init_data_manager("")
    except Exception as e:
        logger.error(f"DataManager初始化失败: {e}")
        return None

def get_data_manager_dependency():
    """获取DataManager依赖"""
    return get_default_data_manager()


@app.post("/chat/stream")
async def chat_stream(
    request: ChatRequest, 
    data_manager: Any = Depends(get_data_manager_dependency),
    current_user: dict = Depends(get_current_active_user)
):
    """
    流式AI聊天接口
    使用集成的AI智能体系统进行分析
    需要用户认证，确保聊天记录隔离
    """
    if not request.message.strip():
        raise HTTPException(status_code=400, detail="消息不能为空")
    
    # 设置用户上下文，确保数据隔离
    from backend.auth.user_context import set_user_context
    set_user_context(user_id=current_user['id'])
    
    logger.info(f"收到AI聊天请求 (用户ID: {current_user['id']}): {request.message[:100]}...")
    
    return StreamingResponse(
        stream_ai_workflow_response(
            user_input=request.message,
            data_manager=data_manager,
            user_id=current_user['id'],
            max_plan_iterations=request.max_plan_iterations,
            max_step_num=request.max_step_num,
            enable_background_investigation=request.enable_background_investigation,
            debug=request.debug
        ),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream",
        }
    )


@app.post("/chat/stream/legacy")
async def chat_stream_legacy(request: ChatRequest):
    """
    旧版流式聊天接口（使用原始的src模块）
    保留用于兼容性
    """
    if not request.message.strip():
        raise HTTPException(status_code=400, detail="消息不能为空")
    
    logger.info(f"收到旧版聊天请求: {request.message[:100]}...")
    
    return StreamingResponse(
        stream_workflow_response(
            user_input=request.message,
            max_plan_iterations=request.max_plan_iterations,
            max_step_num=request.max_step_num,
            enable_background_investigation=request.enable_background_investigation,
            debug=request.debug
        ),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream",
        }
    )


@app.post("/chat")
async def chat_simple(request: ChatRequest):
    """
    简单聊天接口（非流式）
    """
    if not request.message.strip():
        raise HTTPException(status_code=400, detail="消息不能为空")
    
    try:
        # 这里可以实现非流式的处理逻辑
        # 暂时返回一个简单响应
        return {
            "message": "收到消息",
            "user_input": request.message,
            "status": "processing"
        }
    except Exception as e:
        logger.error(f"聊天处理错误: {e}")
        raise HTTPException(status_code=500, detail=f"处理错误: {str(e)}")


# 创建全局实例
# factor_calculator = FactorCalculator() # 将在请求中动态初始化
ml_model_manager = MLModelManager()
model_predictor = ModelPredictor(ml_model_manager)
factor_scorer = FactorScorer()
ml_scorer = MLScorer(ml_model_manager)
data_source_manager = get_data_source_manager()
alternative_data_source = get_alternative_data_source()
backtest_engine = get_backtest_engine()
risk_analyzer = get_risk_analyzer()
risk_monitor = get_risk_monitor()


@app.get("/factors")
async def get_factors():
    """获取所有内置因子列表"""
    try:
        factors = get_all_factors()
        return {"factors": factors}
    except Exception as e:
        logger.error(f"获取因子列表错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取因子列表错误: {str(e)}")


@app.post("/factors/calculate")
async def calculate_factors(request: FactorRequest):
    """计算指定股票的因子值"""
    try:
        logger.info(f"计算因子: 股票={request.symbol}, 因子={request.factors}")
        
        # 动态获取 DataManager 实例
        data_manager = init_data_manager(request.tushare_token)
        
        # 获取股票 OHLCV 数据
        # 默认获取最近5年的数据，如果未提供日期范围
        if not request.start_date:
            import datetime
            end_date = datetime.date.today().strftime("%Y%m%d")
            start_date = (datetime.date.today() - datetime.timedelta(days=5*365)).strftime("%Y%m%d")
        else:
            start_date = request.start_date
            end_date = request.end_date if request.end_date else datetime.date.today().strftime("%Y%m%d")
            
        ohlcv_data = data_manager.get_stock_data(
            ts_code=request.symbol, 
            start_date=start_date, 
            end_date=end_date
        )
        
        if ohlcv_data.empty:
            raise HTTPException(status_code=404, detail=f"未找到股票 {request.symbol} 的数据，请检查数据是否已初始化或Tushare Token是否有效。")

        # 动态创建 FactorCalculator 实例
        factor_calculator_instance = FactorCalculator()

        factors = factor_calculator_instance.calculate_all_factors(
            data=ohlcv_data,
            factor_ids=request.factors
        )
        
        # 格式化因子值，保留4位小数
        formatted_factors = {}
        for factor_id, value in factors.items():
            if isinstance(value, (int, float, np.floating)): # 增加 np.floating 兼容 NumPy 浮点类型
                formatted_factors[factor_id] = round(float(value), 4)
            else:
                formatted_factors[factor_id] = value
        
        return formatted_factors
        
    except Exception as e:
        logger.error(f"计算因子错误: {e}")
        raise HTTPException(status_code=500, detail=f"计算因子错误: {str(e)}")


@app.get("/factors/metadata")
async def get_factor_metadata():
    """获取因子元数据信息"""
    try:
        from backend.core.analysis.factors.basic_factors import FACTOR_METADATA
        return {"metadata": FACTOR_METADATA}
    except Exception as e:
        logger.error(f"获取因子元数据错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取因子元数据错误: {str(e)}")


@app.post("/factors/calculate/smart")
async def calculate_factors_smart(request: FactorRequest):
    """智能因子计算：自动检查数据并在需要时下载"""
    try:
        logger.info(f"智能计算因子: 股票={request.symbol}, 因子={request.factors}")
        
        # 动态获取 DataManager 实例
        data_manager = init_data_manager(request.tushare_token)
        
        # 检查数据库中是否存在该股票的数据
        existing_data = data_manager.get_stock_data(ts_code=request.symbol)
        
        # 如果没有数据或数据不是最新的，则下载数据
        need_download = False
        if existing_data.empty:
            logger.info(f"股票 {request.symbol} 无历史数据，需要下载")
            need_download = True
        else:
            # 检查最新数据日期
            latest_date = data_manager.get_latest_trade_date(request.symbol)
            today = datetime.date.today().strftime("%Y%m%d")
            
            # 如果最新数据超过3天，则更新数据
            if latest_date:
                latest_dt = datetime.datetime.strptime(latest_date, '%Y%m%d')
                days_old = (datetime.datetime.now() - latest_dt).days
                if days_old > 3:
                    logger.info(f"股票 {request.symbol} 数据已过时 {days_old} 天，需要更新")
                    need_download = True
            else:
                need_download = True
        
        # 如果需要下载数据
        if need_download:
            logger.info(f"开始下载/更新股票 {request.symbol} 的数据")
            
            # 创建股票基本信息（如果不存在）
            stocks_in_db = data_manager.get_available_stocks()
            symbol_short = request.symbol.split('.')[0] if '.' in request.symbol else request.symbol
            
            if symbol_short not in stocks_in_db:
                # 创建股票基本信息
                if request.symbol.endswith(('.SZ', '.SH')):
                    # 中国股票
                    market = 'SZ' if request.symbol.endswith('.SZ') else 'SH'
                    stock_info = pd.DataFrame([{
                        'ts_code': request.symbol,
                        'symbol': symbol_short,
                        'name': f'股票{symbol_short}',
                        'market': market,
                        'industry': '未知'
                    }])
                else:
                    # 美股
                    stock_info = pd.DataFrame([{
                        'ts_code': request.symbol,
                        'symbol': request.symbol,
                        'name': f'{request.symbol} Inc.',
                        'market': 'NASDAQ',
                        'industry': '未知'
                    }])
                
                data_manager.save_stock_info(stock_info)
                logger.info(f"创建了股票 {request.symbol} 的基本信息")
            
            # 下载历史数据
            start_date = request.start_date
            if not start_date:
                # 默认下载最近2年的数据
                start_date = (datetime.datetime.now() - datetime.timedelta(days=2*365)).strftime("%Y%m%d")
            
            end_date = request.end_date if request.end_date else datetime.date.today().strftime("%Y%m%d")
            
            # 使用TushareDataManager下载数据
            daily_data = data_manager.tushare_data_manager.download_daily_data(
                request.symbol, start_date, end_date
            )
            
            if not daily_data.empty:
                # 保存数据
                data_manager.save_daily_data(daily_data, request.symbol)
                
                # 计算并保存因子
                data_manager._calculate_and_save_factors(request.symbol, daily_data)
                
                logger.info(f"成功下载并保存了股票 {request.symbol} 的数据和因子")
            else:
                logger.warning(f"无法下载股票 {request.symbol} 的数据")
        
        # 获取最新的股票数据
        ohlcv_data = data_manager.get_stock_data(
            ts_code=request.symbol,
            start_date=request.start_date,
            end_date=request.end_date
        )
        
        if ohlcv_data.empty:
            raise HTTPException(
                status_code=404, 
                detail=f"无法获取股票 {request.symbol} 的数据，请检查股票代码或网络连接"
            )
        
        # 创建 FactorCalculator 实例并计算因子
        factor_calculator_instance = FactorCalculator()
        factors = factor_calculator_instance.calculate_all_factors(
            data=ohlcv_data,
            factor_ids=request.factors
        )
        
        # 格式化因子值
        formatted_factors = {}
        for factor_id, value in factors.items():
            if isinstance(value, (int, float, np.floating)):
                formatted_factors[factor_id] = round(float(value), 4)
            else:
                formatted_factors[factor_id] = value
        
        # 添加元数据信息
        result = {
            "symbol": request.symbol,
            "factors": formatted_factors,
            "data_info": {
                "total_records": len(ohlcv_data),
                "date_range": {
                    "start": ohlcv_data['trade_date'].min() if not ohlcv_data.empty else None,
                    "end": ohlcv_data['trade_date'].max() if not ohlcv_data.empty else None
                },
                "was_downloaded": need_download
            },
            "timestamp": datetime.datetime.now().isoformat()
        }
        
        return result
        
    except Exception as e:
        logger.error(f"智能计算因子错误: {e}")
        raise HTTPException(status_code=500, detail=f"智能计算因子错误: {str(e)}")


@app.post("/factors/timeseries")
async def get_factor_timeseries(request: FactorRequest):
    """获取因子时间序列数据用于绘制图表"""
    try:
        logger.info(f"获取因子时间序列: 股票={request.symbol}, 因子={request.factors}")
        
        # 动态获取 DataManager 实例
        data_manager = init_data_manager(request.tushare_token)
        
        # 获取股票数据
        ohlcv_data = data_manager.get_stock_data(
            ts_code=request.symbol,
            start_date=request.start_date,
            end_date=request.end_date
        )
        
        if ohlcv_data.empty:
            raise HTTPException(
                status_code=404, 
                detail=f"无法获取股票 {request.symbol} 的数据，请检查股票代码"
            )
        
        # 确保数据按日期排序
        ohlcv_data = ohlcv_data.sort_values('trade_date')
        
        # 创建 FactorCalculator 实例
        factor_calculator_instance = FactorCalculator()
        
        # 计算每日的因子值
        factor_timeseries = {}
        dates = []
        
        # 获取要计算的因子列表
        factors_to_calculate = request.factors if request.factors else ['rsi', 'macd', 'pe_ratio']
        
        # 为每个因子初始化时间序列
        for factor_id in factors_to_calculate:
            factor_timeseries[factor_id] = []
        
        # 计算滑动窗口的因子值
        min_window = 50  # 最小窗口大小，确保有足够数据计算技术指标
        
        for i in range(min_window, len(ohlcv_data)):
            # 获取当前窗口的数据
            window_data = ohlcv_data.iloc[:i+1].copy()
            current_date = window_data['trade_date'].iloc[-1]
            
            # 计算当前时点的因子值
            try:
                factors = factor_calculator_instance.calculate_all_factors(
                    data=window_data,
                    factor_ids=factors_to_calculate
                )
                
                # 记录日期（只在第一次添加）
                if len(dates) == i - min_window:
                    dates.append(current_date)
                
                # 记录每个因子的值
                for factor_id in factors_to_calculate:
                    if factor_id in factors:
                        value = factors[factor_id]
                        if isinstance(value, (int, float, np.floating)):
                            factor_timeseries[factor_id].append(float(value))
                        else:
                            factor_timeseries[factor_id].append(0.0)
                    else:
                        factor_timeseries[factor_id].append(0.0)
                        
            except Exception as e:
                logger.warning(f"计算第{i}个时点的因子时出错: {e}")
                # 添加默认值
                if len(dates) == i - min_window:
                    dates.append(current_date)
                for factor_id in factors_to_calculate:
                    factor_timeseries[factor_id].append(0.0)
        
        # 对每个因子进行归一化处理
        normalized_factors = {}
        for factor_id, values in factor_timeseries.items():
            if len(values) > 0:
                values_array = np.array(values)
                # 使用Min-Max归一化到[0,1]区间
                min_val = np.min(values_array)
                max_val = np.max(values_array)
                if max_val > min_val:
                    normalized_values = (values_array - min_val) / (max_val - min_val)
                else:
                    normalized_values = np.ones_like(values_array) * 0.5
                normalized_factors[factor_id] = normalized_values.tolist()
            else:
                normalized_factors[factor_id] = []
        
        # 构建返回结果
        result = {
            "symbol": request.symbol,
            "dates": dates,
            "factors": factor_timeseries,  # 原始因子值
            "normalized_factors": normalized_factors,  # 归一化后的因子值
            "data_info": {
                "total_records": len(ohlcv_data),
                "timeseries_points": len(dates),
                "date_range": {
                    "start": dates[0] if dates else None,
                    "end": dates[-1] if dates else None
                }
            },
            "timestamp": datetime.datetime.now().isoformat()
        }
        
        return result
        
    except Exception as e:
        logger.error(f"获取因子时间序列错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取因子时间序列错误: {str(e)}")


def generate_factor_formula(description: str) -> dict:
    """使用LLM生成因子公式"""
    try:
        # 导入LLM相关模块
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
        
        from src.llms.llm import get_llm_by_type
        
        # 构建更专业的提示词
        prompt = f"""你是一位专业的量化金融分析师，擅长设计技术分析因子。请根据用户的描述生成一个可执行的因子计算公式。

用户描述：{description}

请生成一个JSON格式的响应，包含以下字段：
- factor_name: 因子名称（简洁明了）
- formula: Python计算公式（简洁的表达式，避免复杂的if-else结构）
- explanation: 因子的详细解释和投资逻辑
- category: 因子类别（technical、fundamental、chip之一）
- confidence: 置信度（0-1之间的数值）

公式编写要求：
1. 直接编写可执行的代码，变量名为df（pandas DataFrame）
2. 包含close、high、low、volume、open等列
3. 最后使用"result = 表达式"的形式返回结果
4. 尽量避免复杂的if-else控制结构，使用简洁的数学表达式
5. 处理边界情况时使用.fillna()、.dropna()等pandas方法
6. 可以使用numpy（np）和pandas（pd）函数

示例格式：
{{
    "factor_name": "RSI指标",
    "formula": "delta = df['close'].diff()\\nup = delta.clip(lower=0).rolling(14).mean()\\ndown = (-delta).clip(lower=0).rolling(14).mean()\\nrs = up / down.replace(0, 1e-10)\\nrsi = 100 - (100 / (1 + rs))\\nresult = float(rsi.iloc[-1]) if len(rsi) > 0 and not pd.isna(rsi.iloc[-1]) else 50.0",
    "explanation": "计算14日RSI相对强弱指标，返回最新的RSI值",
    "category": "technical", 
    "confidence": 0.9
}}

重要提醒：
- 公式尽量简洁，避免多层嵌套的if-else
- 使用pandas内置方法处理异常情况
- 最后一行必须是"result = 表达式"的格式
- 使用\\n表示换行符

请生成一个专业、简洁的因子设计："""

        # 调用LLM
        llm = get_llm_by_type("basic")
        response = llm.invoke(prompt)
        
        # 解析响应
        import json
        import re
        
        # 尝试提取JSON内容
        content = response.content if hasattr(response, 'content') else str(response)
        
        # 查找JSON块
        json_match = re.search(r'\{.*\}', content, re.DOTALL)
        if json_match:
            try:
                result = json.loads(json_match.group())
                
                # 验证必需字段
                required_fields = ['factor_name', 'formula', 'explanation', 'confidence']
                for field in required_fields:
                    if field not in result:
                        raise ValueError(f"Missing required field: {field}")
                
                # 格式化公式：确保换行符正确，移除函数定义
                if 'formula' in result:
                    formula = result['formula']
                    # 处理转义的换行符
                    formula = formula.replace('\\n', '\n')
                    
                    # 确保最后一行是result赋值
                    lines = formula.split('\n')
                    cleaned_lines = []
                    
                    for line in lines:
                        line_stripped = line.strip()
                        # 跳过函数定义相关行
                        if line_stripped.startswith('def ') or line_stripped.startswith('import '):
                            continue
                        elif line_stripped.startswith('return '):
                            # 将return语句转换为result赋值
                            return_value = line_stripped[7:]  # 去掉'return '
                            cleaned_lines.append(f"result = {return_value}")
                        elif line_stripped and not line_stripped.startswith('#'):
                            cleaned_lines.append(line_stripped)
                    
                    # 重新组装代码
                    result['formula'] = '\n'.join(cleaned_lines)
                
                # 设置默认类别
                if 'category' not in result:
                    result['category'] = 'technical'
                
                # 确保置信度在合理范围内
                if not isinstance(result['confidence'], (int, float)) or not (0 <= result['confidence'] <= 1):
                    result['confidence'] = 0.8
                
                logger.info(f"LLM成功生成因子: {result['factor_name']}")
                return result
                
            except json.JSONDecodeError as e:
                logger.warning(f"LLM响应JSON解析失败: {e}，使用备用方案")
        
        # 如果LLM响应解析失败，使用备用逻辑
        return fallback_factor_generation(description, content)
        
    except Exception as e:
        logger.error(f"LLM调用失败: {e}，使用备用方案")
        return fallback_factor_generation(description, "")

def fallback_factor_generation(description: str, llm_content: str = "") -> dict:
    """备用的因子生成逻辑"""
    description_lower = description.lower()
    
    # 扩展的因子模板库
    factor_templates = {
        "布林带": {
            "formula": """if len(df) < 20: return 0.5
# 计算布林带
ma20 = df['close'].rolling(window=20).mean()
std20 = df['close'].rolling(window=20).std()
multiplier = {multiplier}
upper_band = ma20 + (std20 * multiplier)
lower_band = ma20 - (std20 * multiplier)
current_price = df['close'].iloc[-1]
# 计算价格在布林带中的相对位置
if upper_band.iloc[-1] != lower_band.iloc[-1]:
    position = (current_price - lower_band.iloc[-1]) / (upper_band.iloc[-1] - lower_band.iloc[-1])
    return float(position)
else:
    return 0.5""",
            "explanation": "计算当前价格在布林带中的相对位置，返回0-1之间的数值",
            "category": "technical"
        },
        "rsi": {
            "formula": """if len(df) < 15: return 50.0
# 计算RSI指标
delta = df['close'].diff()
gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
# 避免除零错误
rs = gain / loss.replace(0, 1e-10)
rsi = 100 - (100 / (1 + rs))
rsi_value = float(rsi.iloc[-1]) if not pd.isna(rsi.iloc[-1]) else 50.0
return rsi_value""",
            "explanation": "相对强弱指标，返回0-100之间的数值，用于判断超买超卖状态",
            "category": "technical"
        },
        "macd": {
            "formula": """if len(df) < 26: return 0.0
# 计算MACD指标
ema12 = df['close'].ewm(span=12, adjust=False).mean()
ema26 = df['close'].ewm(span=26, adjust=False).mean()
macd_line = ema12 - ema26
signal_line = macd_line.ewm(span=9, adjust=False).mean()
# 返回MACD柱状图值
histogram = macd_line - signal_line
return float(histogram.iloc[-1]) if not pd.isna(histogram.iloc[-1]) else 0.0""",
            "explanation": "MACD柱状图值，用于判断趋势变化和买卖信号",
            "category": "technical"
        },
        "成交量": {
            "formula": """if len(df) < 20 or 'volume' not in df.columns: return 1.0
# 计算成交量比率因子
volume_ma = df['volume'].rolling(window=20).mean()
current_volume = df['volume'].iloc[-1]
# 计算当前成交量相对于平均成交量的比率
if volume_ma.iloc[-1] > 0:
    volume_ratio = float(current_volume / volume_ma.iloc[-1])
    return volume_ratio
else:
    return 1.0""",
            "explanation": "当前成交量相对于20日平均成交量的比率",
            "category": "technical"
        },
        "波动率": {
            "formula": """if len(df) < 20: return 0.0
# 计算价格波动率因子
returns = df['close'].pct_change().dropna()
if len(returns) < 20:
    return 0.0
# 计算20日滚动标准差（年化）
volatility = returns.rolling(window=20).std()
annualized_vol = volatility.iloc[-1] * (252 ** 0.5)  # 年化波动率
return float(annualized_vol) if not pd.isna(annualized_vol) else 0.0""",
            "explanation": "20日价格波动率（年化），反映股价的波动程度",
            "category": "technical"
        }
    }
    
    # 查找匹配的因子类型
    matched_factor = None
    for factor_type in factor_templates.keys():
        if factor_type.lower() in description_lower:
            matched_factor = factor_type
            break
    
    # 如果没有匹配，根据关键词判断
    if not matched_factor:
        if any(keyword in description_lower for keyword in ['rsi', '相对强弱', '超买', '超卖']):
            matched_factor = "rsi"
        elif any(keyword in description_lower for keyword in ['macd', '金叉', '死叉', '背离']):
            matched_factor = "macd"
        elif any(keyword in description_lower for keyword in ['布林', 'bollinger', '通道']):
            matched_factor = "布林带"
        elif any(keyword in description_lower for keyword in ['成交量', 'volume', '放量']):
            matched_factor = "成交量"
        elif any(keyword in description_lower for keyword in ['波动', 'volatility', '风险']):
            matched_factor = "波动率"
        else:
            matched_factor = "rsi"  # 默认使用RSI
    
    template = factor_templates[matched_factor]
    
    # 从描述中提取数值参数
    import re
    numbers = re.findall(r'\d+', description)
    
    # 替换模板中的参数
    formula = template["formula"]
    if "{multiplier}" in formula:
        multiplier = numbers[0] if numbers else "2"
        formula = formula.replace("{multiplier}", multiplier)
    
    # 生成因子名称
    factor_name = f"{matched_factor}因子"
    if numbers:
        factor_name += f"({numbers[0]}期)" if len(numbers) == 1 else f"({','.join(numbers[:2])})"
    
    # 检查是否有特殊条件修饰词
    if "超买" in description:
        factor_name = f"{matched_factor}超买因子"
        # 为超买因子添加条件判断
        if matched_factor == "rsi":
            formula += "\n# 超买判断：RSI > 70时返回1，否则返回0\nreturn 1.0 if rsi_value > 70 else 0.0"
    elif "超卖" in description:
        factor_name = f"{matched_factor}超卖因子"
        if matched_factor == "rsi":
            formula += "\n# 超卖判断：RSI < 30时返回1，否则返回0\nreturn 1.0 if rsi_value < 30 else 0.0"
    elif "金叉" in description:
        factor_name = f"{matched_factor}金叉因子"
        if matched_factor == "macd":
            formula = """if len(df) < 26: return 0.0
# 计算MACD组件
ema_12 = df['close'].ewm(span=12, adjust=False).mean()
ema_26 = df['close'].ewm(span=26, adjust=False).mean()
macd_line = ema_12 - ema_26
signal_line = macd_line.ewm(span=9, adjust=False).mean()
# 检查金叉（MACD上穿信号线）
if len(macd_line) >= 2:
    current_diff = macd_line.iloc[-1] - signal_line.iloc[-1]
    previous_diff = macd_line.iloc[-2] - signal_line.iloc[-2]
    return 1.0 if current_diff > 0 and previous_diff <= 0 else 0.0
else:
    return 0.0"""
    elif "死叉" in description:
        factor_name = f"{matched_factor}死叉因子"
        if matched_factor == "macd":
            formula = """if len(df) < 26: return 0.0
# 计算MACD组件
ema_12 = df['close'].ewm(span=12, adjust=False).mean()
ema_26 = df['close'].ewm(span=26, adjust=False).mean()
macd_line = ema_12 - ema_26
signal_line = macd_line.ewm(span=9, adjust=False).mean()
# 检查死叉（MACD下穿信号线）
if len(macd_line) >= 2:
    current_diff = macd_line.iloc[-1] - signal_line.iloc[-1]
    previous_diff = macd_line.iloc[-2] - signal_line.iloc[-2]
    return 1.0 if current_diff < 0 and previous_diff >= 0 else 0.0
else:
    return 0.0"""
    
    return {
        "factor_name": factor_name,
        "formula": formula,
        "explanation": template["explanation"],
        "category": template["category"],
        "confidence": 0.85 if llm_content else 0.7
    }

@app.post("/factors/ai-generate")
async def ai_generate_factor(request: AIFactorRequest):
    """AI生成因子公式"""
    try:
        logger.info(f"AI生成因子: 描述={request.description}")
        
        result = generate_factor_formula(request.description)
        
        return {
            "success": True,
            "factor_name": result["factor_name"],
            "formula": result["formula"],
            "explanation": result["explanation"],
            "confidence": result["confidence"],
            "category": result.get("category", "technical"),
            "timestamp": datetime.datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"AI生成因子错误: {e}")
        raise HTTPException(status_code=500, detail=f"AI生成因子错误: {str(e)}")

# 自定义因子测试相关模型
class CustomFactorTestRequest(BaseModel):
    symbol: str
    formula: str
    tushare_token: Optional[str] = None

@app.post("/factors/test-custom")
async def test_custom_factor(request: CustomFactorTestRequest):
    """测试自定义因子公式"""
    try:
        logger.info(f"测试自定义因子: 股票={request.symbol}")
        
        # 获取数据管理器
        data_manager = await get_data_manager_dependency(request.tushare_token)
        
        # 获取股票数据
        df = data_manager.get_stock_data(request.symbol)
        if df is None or len(df) == 0:
            # 尝试下载数据
            df = data_manager.download_stock_data(request.symbol)
        
        if df is None or len(df) == 0:
            raise HTTPException(status_code=404, detail=f"无法获取股票 {request.symbol} 的数据")
        
        # 执行自定义因子公式
        result = execute_custom_factor_formula(df, request.formula)
        
        return {
            "success": True,
            "symbol": request.symbol,
            "factor_value": result,
            "data_points": len(df),
            "timestamp": datetime.datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"测试自定义因子错误: {e}")
        raise HTTPException(status_code=500, detail=f"测试自定义因子错误: {str(e)}")

def execute_custom_factor_formula(df: pd.DataFrame, formula: str) -> float:
    """安全执行自定义因子公式"""
    try:
        # 准备执行环境
        import numpy as np
        import pandas as pd
        
        # 创建独立的执行命名空间
        exec_namespace = {
            'df': df,
            'np': np,
            'pd': pd,
            'len': len,
            'float': float,
            'int': int,
            'max': max,
            'min': min,
            'abs': abs,
            'round': round,
            'result': None  # 用于存储返回值
        }
        
        # 清理公式（处理转义字符）
        formula = formula.replace('\\n', '\n').replace('\\t', '\t')
        
        # 将return语句替换为赋值语句
        lines = formula.split('\n')
        processed_lines = []
        
        for line in lines:
            stripped_line = line.strip()
            if not stripped_line:
                continue
                
            # 替换return语句为赋值
            if stripped_line.startswith('return '):
                return_value = stripped_line[7:]  # 去掉'return '
                processed_lines.append(f"result = {return_value}")
            else:
                processed_lines.append(stripped_line)
        
        # 重新组装代码
        processed_formula = '\n'.join(processed_lines)
        
        logger.info(f"处理后的公式:\n{processed_formula}")
        
        # 直接执行代码
        exec(processed_formula, exec_namespace)
        
        # 获取结果
        result = exec_namespace.get('result', 0.0)
        
        # 确保返回数值类型
        if result is None or pd.isna(result):
            return 0.0
        
        return float(result)
        
    except Exception as e:
        logger.error(f"执行自定义因子公式错误: {e}")
        logger.error(f"原始公式: {formula}")
        if 'processed_formula' in locals():
            logger.error(f"处理后公式: {processed_formula}")
        raise ValueError(f"公式执行错误: {str(e)}")


# ==================== MACD背离检测API ====================

@app.post("/divergence/scan")
async def scan_market_divergence(request: DivergenceScanRequest):
    """扫描市场MACD背离"""
    try:
        from backend.core.analysis.technical.market_scanner import get_market_scanner
        
        logger.info(f"开始扫描{request.market}市场的MACD背离")
        
        # 获取市场扫描器
        scanner = get_market_scanner(request.tushare_token)
        
        # 执行扫描
        results = await scanner.scan_market(
            market=request.market,
            divergence_types=request.divergence_types or ['bullish', 'bearish']
        )
        
        return {
            "success": True,
            "data": results,
            "timestamp": datetime.datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"扫描市场背离错误: {e}")
        raise HTTPException(status_code=500, detail=f"扫描市场背离错误: {str(e)}")

@app.get("/divergence/recent/{market}")
async def get_recent_divergences(market: str, hours: int = 24):
    """获取最近的背离信号"""
    try:
        from backend.core.analysis.technical.divergence_detector import get_divergence_database
        
        db = get_divergence_database()
        divergences = db.get_recent_divergences(market, hours)
        
        return {
            "success": True,
            "market": market,
            "hours": hours,
            "count": len(divergences),
            "divergences": divergences,
            "timestamp": datetime.datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取最近背离信号错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取最近背离信号错误: {str(e)}")

@app.get("/divergence/history/{market}")
async def get_scan_history(market: str, limit: int = 10):
    """获取扫描历史"""
    try:
        from backend.core.analysis.technical.market_scanner import get_market_scanner
        
        # 使用默认token获取扫描器（仅用于查询历史）
        scanner = get_market_scanner("dummy_token")
        history = scanner.get_scan_history(market, limit)
        
        return {
            "success": True,
            "market": market,
            "count": len(history),
            "history": history,
            "timestamp": datetime.datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取扫描历史错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取扫描历史错误: {str(e)}")

@app.get("/divergence/chart/{symbol}")
async def get_divergence_chart(symbol: str, market: str):
    """获取股票的背离图表"""
    try:
        from backend.core.analysis.technical.divergence_detector import get_divergence_database
        
        db = get_divergence_database()
        
        # 查询该股票最近的背离信号
        with db.get_connection() as conn:
            cursor = conn.execute("""
                SELECT chart_image FROM divergence_signals 
                WHERE symbol = ? AND market = ? 
                ORDER BY detected_at DESC LIMIT 1
            """, (symbol, market))
            result = cursor.fetchone()
        
        if result and result['chart_image']:
            return {
                "success": True,
                "symbol": symbol,
                "market": market,
                "chart_image": result['chart_image'],
                "timestamp": datetime.datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=404, detail=f"未找到{symbol}的背离图表")
        
    except Exception as e:
        logger.error(f"获取背离图表错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取背离图表错误: {str(e)}")

@app.get("/divergence/markets")
async def get_supported_markets():
    """获取支持的市场列表"""
    return {
        "success": True,
        "markets": {
            "US": {
                "name": "美股",
                "description": "美国股票市场",
                "sample_stocks": ["AAPL", "MSFT", "GOOGL", "AMZN", "TSLA"]
            },
            "HK": {
                "name": "港股", 
                "description": "香港股票市场",
                "sample_stocks": ["00700.HK", "00941.HK", "03690.HK", "00175.HK"]
            },
            "CN": {
                "name": "A股",
                "description": "中国A股市场", 
                "sample_stocks": ["000001.SZ", "600519.SH", "000858.SZ", "600036.SH"]
            }
        },
        "divergence_types": {
            "bullish": "底背离（看涨信号）",
            "bearish": "顶背离（看跌信号）"
        },
        "timestamp": datetime.datetime.now().isoformat()
    }

@app.post("/stocks/query")
async def query_stock_data(request: StockDataQueryRequest):
    """查询股票数据（包含K线数据和基本信息）"""
    try:
        logger.info(f"查询股票数据: {request.symbol}")
        
        # 动态获取 DataManager 实例
        data_manager = init_data_manager(request.tushare_token)
        
        # 解析时间范围
        if request.period and not request.start_date:
            import datetime
            end_date = datetime.date.today()
            
            if request.period == "1m":
                start_date = end_date - datetime.timedelta(days=30)
            elif request.period == "3m":
                start_date = end_date - datetime.timedelta(days=90)
            elif request.period == "6m":
                start_date = end_date - datetime.timedelta(days=180)
            elif request.period == "1y":
                start_date = end_date - datetime.timedelta(days=365)
            elif request.period == "2y":
                start_date = end_date - datetime.timedelta(days=730)
            elif request.period == "5y":
                start_date = end_date - datetime.timedelta(days=1825)
            else:
                start_date = end_date - datetime.timedelta(days=365)
                
            start_date_str = start_date.strftime("%Y%m%d")
            end_date_str = end_date.strftime("%Y%m%d")
        else:
            start_date_str = request.start_date
            end_date_str = request.end_date
        
        # 检查数据库中是否有数据
        existing_data = data_manager.get_stock_data(
            ts_code=request.symbol,
            start_date=start_date_str,
            end_date=end_date_str
        )
        
        # 如果数据不足，从akshare下载
        need_download = False
        if existing_data.empty:
            logger.info(f"股票 {request.symbol} 无数据，需要下载")
            need_download = True
        else:
            # 检查数据完整性
            expected_days = (datetime.datetime.strptime(end_date_str, "%Y%m%d") - 
                           datetime.datetime.strptime(start_date_str, "%Y%m%d")).days
            actual_days = len(existing_data)
            
            # 如果数据量少于预期的50%，则重新下载
            if actual_days < expected_days * 0.5:
                logger.info(f"股票 {request.symbol} 数据不完整({actual_days}/{expected_days})，需要下载")
                need_download = True
        
        if need_download:
            try:
                # 确保股票基本信息存在
                stocks_in_db = data_manager.get_available_stocks()
                symbol_short = request.symbol.split('.')[0] if '.' in request.symbol else request.symbol
                
                if symbol_short not in stocks_in_db:
                    # 创建股票基本信息
                    if request.symbol.endswith(('.SZ', '.SH')):
                        # 中国股票
                        market = 'SZ' if request.symbol.endswith('.SZ') else 'SH'
                        stock_info = pd.DataFrame([{
                            'ts_code': request.symbol,
                            'symbol': symbol_short,
                            'name': f'股票{symbol_short}',
                            'market': market,
                            'industry': '未知'
                        }])
                    else:
                        # 美股/港股
                        market = 'HK' if request.symbol.endswith('.HK') else 'NASDAQ'
                        stock_info = pd.DataFrame([{
                            'ts_code': request.symbol,
                            'symbol': request.symbol,
                            'name': f'{request.symbol}',
                            'market': market,
                            'industry': '未知'
                        }])
                    
                    data_manager.save_stock_info(stock_info)
                    logger.info(f"创建了股票 {request.symbol} 的基本信息")
                
                # 使用智能数据获取
                downloaded_data = data_manager.get_stock_data_intelligent(
                    symbol=request.symbol,
                    start_date=start_date_str,
                    end_date=end_date_str,
                    force_download=True
                )
                
                if not downloaded_data.empty:
                    # 保存数据
                    data_manager.save_daily_data(downloaded_data, request.symbol)
                    logger.info(f"成功下载并保存股票 {request.symbol} 的数据")
                    existing_data = downloaded_data
                else:
                    logger.warning(f"无法下载股票 {request.symbol} 的数据")
                    
            except Exception as e:
                logger.error(f"下载股票数据失败: {e}")
                if existing_data.empty:
                    raise HTTPException(
                        status_code=404, 
                        detail=f"无法获取股票 {request.symbol} 的数据: {str(e)}"
                    )
        
        # 获取最终数据
        final_data = data_manager.get_stock_data(
            ts_code=request.symbol,
            start_date=start_date_str,
            end_date=end_date_str
        )
        
        if final_data.empty:
            raise HTTPException(
                status_code=404,
                detail=f"无法获取股票 {request.symbol} 的数据"
            )
        
        # 验证必要的列是否存在
        required_columns = ['trade_date', 'close']
        missing_columns = [col for col in required_columns if col not in final_data.columns]
        if missing_columns:
            logger.error(f"股票数据缺少必要的列: {missing_columns}")
            raise HTTPException(status_code=500, detail=f"数据格式错误，缺少列: {missing_columns}")
        
        # 确保数据按日期排序
        final_data = final_data.sort_values('trade_date').reset_index(drop=True)
        
        # 移除空的行
        final_data = final_data.dropna(subset=['trade_date', 'close'])
        
        # 格式化K线数据
        kline_data = []
        for _, row in final_data.iterrows():
            try:
                # 安全的数据类型转换
                def safe_float(value, default=0.0):
                    if value is None or pd.isna(value):
                        return default
                    try:
                        return float(value)
                    except (ValueError, TypeError):
                        return default
                
                # 处理日期格式
                trade_date = row['trade_date']
                if pd.isna(trade_date):
                    continue
                    
                # 转换为字符串格式
                if hasattr(trade_date, 'strftime'):
                    date_str = trade_date.strftime('%Y%m%d')
                else:
                    date_str = str(trade_date)
                
                kline_data.append({
                    'date': date_str,
                    'open': safe_float(row.get('open')),
                    'high': safe_float(row.get('high')),
                    'low': safe_float(row.get('low')),
                    'close': safe_float(row.get('close')),
                    'volume': safe_float(row.get('volume', row.get('vol', 0))),
                    'amount': safe_float(row.get('amount', 0)),
                    'pct_change': safe_float(row.get('pct_change', 0))
                })
            except Exception as e:
                logger.warning(f"处理第{len(kline_data)}行数据时出错: {e}")
                continue
        
        # 获取股票基本信息
        basic_info = {
            'symbol': request.symbol,
            'name': '未知',
            'market': '未知',
            'industry': '未知'
        }
        
        # 尝试从数据库获取基本信息
        try:
            with data_manager.db_manager.get_connection() as conn:
                cursor = conn.execute(
                    "SELECT symbol, name, market, industry FROM stock_info WHERE ts_code = ?",
                    (request.symbol,)
                )
                result = cursor.fetchone()
                if result:
                    basic_info.update({
                        'name': result['name'],
                        'market': result['market'], 
                        'industry': result['industry']
                    })
        except Exception as e:
            logger.warning(f"获取股票基本信息失败: {e}")
        
        # 计算基本统计信息 - 使用安全的转换函数
        def safe_float_calc(value, default=0.0):
            if value is None or pd.isna(value):
                return default
            try:
                return float(value)
            except (ValueError, TypeError):
                return default
        
        latest_price = safe_float_calc(final_data['close'].iloc[-1]) if len(final_data) > 0 else 0.0
        price_change = safe_float_calc(final_data['pct_change'].iloc[-1]) if 'pct_change' in final_data.columns and len(final_data) > 0 else 0.0
        highest_price = safe_float_calc(final_data['close'].max()) if len(final_data) > 0 else 0.0
        lowest_price = safe_float_calc(final_data['close'].min()) if len(final_data) > 0 else 0.0
        
        # 计算成交量，优先使用volume列，然后是vol列
        volume_col = None
        if 'volume' in final_data.columns:
            volume_col = 'volume'
        elif 'vol' in final_data.columns:
            volume_col = 'vol'
        
        avg_volume = safe_float_calc(final_data[volume_col].mean()) if volume_col and len(final_data) > 0 else 0.0
        
        statistics = {
            'latest_price': latest_price,
            'price_change': price_change,
            'highest_price': highest_price,
            'lowest_price': lowest_price,
            'price_range': highest_price - lowest_price,
            'avg_volume': avg_volume,
            'total_records': len(final_data),
            'date_range': {
                'start': str(final_data['trade_date'].min()) if len(final_data) > 0 else '',
                'end': str(final_data['trade_date'].max()) if len(final_data) > 0 else ''
            }
        }
        
        return {
            'success': True,
            'symbol': request.symbol,
            'basic_info': basic_info,
            'statistics': statistics,
            'kline_data': kline_data,
            'data_source': 'database' if not need_download else 'downloaded',
            'timestamp': datetime.datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"查询股票数据错误: {e}")
        raise HTTPException(status_code=500, detail=f"查询股票数据错误: {str(e)}")


@app.post("/stocks/technical-indicators")
async def get_technical_indicators(request: TechnicalIndicatorRequest):
    """获取股票的技术指标数据"""
    try:
        logger.info(f"计算技术指标: 股票={request.symbol}, 指标={request.indicators}")
        
        # 动态获取 DataManager 实例
        data_manager = init_data_manager(request.tushare_token)
        
        # 解析时间范围
        if request.period:
            import datetime
            end_date = datetime.date.today()
            
            if request.period == "1m":
                start_date = end_date - datetime.timedelta(days=30)
            elif request.period == "3m":
                start_date = end_date - datetime.timedelta(days=90)
            elif request.period == "6m":
                start_date = end_date - datetime.timedelta(days=180)
            elif request.period == "1y":
                start_date = end_date - datetime.timedelta(days=365)
            elif request.period == "2y":
                start_date = end_date - datetime.timedelta(days=730)
            elif request.period == "5y":
                start_date = end_date - datetime.timedelta(days=1825)
            else:
                start_date = end_date - datetime.timedelta(days=365)
                
            start_date_str = start_date.strftime("%Y%m%d")
            end_date_str = end_date.strftime("%Y%m%d")
        else:
            start_date_str = None
            end_date_str = None
        
        # 获取股票数据
        stock_data = data_manager.get_stock_data(
            ts_code=request.symbol,
            start_date=start_date_str,
            end_date=end_date_str
        )
        
        if stock_data.empty:
            raise HTTPException(
                status_code=404,
                detail=f"无法获取股票 {request.symbol} 的数据，请先查询股票数据"
            )
        
        # 确保数据按日期排序
        stock_data = stock_data.sort_values('trade_date').reset_index(drop=True)
        
        # 创建 FactorCalculator 实例
        factor_calculator = FactorCalculator()
        
        # 计算技术指标时间序列
        indicators_data = {}
        dates = stock_data['trade_date'].tolist()
        
        # 计算滑动窗口的指标值
        min_window = 50  # 最小窗口大小
        
        for indicator in request.indicators:
            indicators_data[indicator] = []
        
        # 计算每日的指标值
        for i in range(min_window, len(stock_data)):
            window_data = stock_data.iloc[:i+1].copy()
            
            # 计算当前时点的指标值
            try:
                # 安全的浮点数转换函数
                def safe_indicator_float(value, default=0.0):
                    if value is None or pd.isna(value):
                        return default
                    try:
                        return float(value)
                    except (ValueError, TypeError):
                        return default
                
                if 'sma' in request.indicators:
                    sma_20 = factor_calculator.calculate_sma(window_data['close'].values, 20)
                    indicators_data['sma'].append(safe_indicator_float(sma_20))
                
                if 'ema' in request.indicators:
                    if len(window_data) >= 12:
                        ema_12 = window_data['close'].ewm(span=12).mean().iloc[-1]
                        indicators_data['ema'].append(safe_indicator_float(ema_12))
                    else:
                        indicators_data['ema'].append(safe_indicator_float(window_data['close'].iloc[-1]))
                
                if 'rsi' in request.indicators:
                    rsi = factor_calculator.calculate_rsi(window_data['close'].values)
                    indicators_data['rsi'].append(safe_indicator_float(rsi))
                
                if 'macd' in request.indicators:
                    if len(window_data) >= 26:
                        ema12 = window_data['close'].ewm(span=12).mean().iloc[-1]
                        ema26 = window_data['close'].ewm(span=26).mean().iloc[-1]
                        macd = ema12 - ema26
                        indicators_data['macd'].append(safe_indicator_float(macd))
                    else:
                        indicators_data['macd'].append(0.0)
                
                if 'bollinger' in request.indicators:
                    upper, middle, lower = factor_calculator.calculate_bollinger_bands(window_data['close'].values)
                    indicators_data['bollinger'].append({
                        'upper': safe_indicator_float(upper),
                        'middle': safe_indicator_float(middle), 
                        'lower': safe_indicator_float(lower)
                    })
                        
            except Exception as e:
                logger.warning(f"计算第{i}个时点的指标时出错: {e}")
                # 添加默认值
                for indicator in request.indicators:
                    if indicator == 'bollinger':
                        indicators_data[indicator].append({
                            'upper': 0.0, 'middle': 0.0, 'lower': 0.0
                        })
                    else:
                        indicators_data[indicator].append(0.0)
        
        # 构建返回结果
        result = {
            'success': True,
            'symbol': request.symbol,
            'period': request.period,
            'dates': dates[min_window:],  # 与指标数据对应的日期
            'indicators': indicators_data,
            'data_info': {
                'total_records': len(stock_data),
                'indicator_points': len(dates[min_window:]),
                'date_range': {
                    'start': dates[min_window] if len(dates) > min_window else None,
                    'end': dates[-1] if dates else None
                }
            },
            'timestamp': datetime.datetime.now().isoformat()
        }
        
        return result
        
    except Exception as e:
        logger.error(f"计算技术指标错误: {e}")
        raise HTTPException(status_code=500, detail=f"计算技术指标错误: {str(e)}")


# 全局缓存变量
_tushare_stock_cache = None
_akshare_us_stock_cache = None
_cache_timestamp = None
_akshare_cache_timestamp = None
_cache_duration = 3600  # 缓存1小时

def get_tushare_stock_data():
    """从Tushare API获取股票数据（带缓存）"""
    global _tushare_stock_cache, _cache_timestamp

    try:
        import time
        current_time = time.time()

        # 检查缓存是否有效
        if (_tushare_stock_cache is not None and
            _cache_timestamp is not None and
            current_time - _cache_timestamp < _cache_duration):
            logger.info(f"使用缓存的Tushare数据，共 {len(_tushare_stock_cache)} 只股票")
            return _tushare_stock_cache

        import tushare as ts
        import os
        from dotenv import load_dotenv

        # 加载环境变量
        load_dotenv()
        token = os.getenv('TUSHARE_TOKEN')

        if not token:
            logger.warning("未找到TUSHARE_TOKEN，跳过Tushare数据获取")
            return {}

        # 初始化Tushare Pro API
        pro = ts.pro_api(token)
        stock_database = {}

        # 获取A股数据（限制数量以提高响应速度）
        try:
            logger.info("正在从Tushare获取A股数据...")
            a_stocks = pro.stock_basic(
                exchange='',
                list_status='L',
                fields='ts_code,symbol,name,area,industry'
            )

            for _, row in a_stocks.iterrows():
                symbol = row['symbol']
                stock_database[symbol] = {
                    'name': row['name'],
                    'market': 'A股',
                    'industry': row['industry'] or '未知'
                }
                # 同时添加带后缀的代码
                ts_code = row['ts_code']
                stock_database[ts_code] = {
                    'name': row['name'],
                    'market': 'A股',
                    'industry': row['industry'] or '未知'
                }

            logger.info(f"获取到 {len(a_stocks)} 只A股数据")

        except Exception as e:
            logger.warning(f"获取A股数据失败: {e}")

        # 获取港股数据（限制数量）
        try:
            logger.info("正在从Tushare获取港股数据...")
            hk_stocks = pro.hk_basic(list_status='L')

            # 只取前200只港股以提高响应速度
            hk_stocks = hk_stocks.head(200)

            for _, row in hk_stocks.iterrows():
                ts_code = row['ts_code']
                stock_database[ts_code] = {
                    'name': row['name'],
                    'market': '港股',
                    'industry': '未知'
                }

            logger.info(f"获取到 {len(hk_stocks)} 只港股数据")

        except Exception as e:
            logger.warning(f"获取港股数据失败: {e}")

        # 获取美股数据（限制数量）
        try:
            logger.info("正在从Tushare获取美股数据...")
            us_stocks = pro.us_basic(limit=300)  # 限制300只美股

            # 过滤掉没有名称的股票
            us_stocks = us_stocks.dropna(subset=['name'])

            for _, row in us_stocks.iterrows():
                ts_code = row['ts_code']
                if pd.notna(row['name']) and row['name'].strip():
                    stock_database[ts_code] = {
                        'name': row['name'],
                        'market': '美股',
                        'industry': '未知'
                    }

            logger.info(f"获取到 {len(us_stocks)} 只美股数据")

        except Exception as e:
            logger.warning(f"获取美股数据失败: {e}")

        # 更新缓存
        _tushare_stock_cache = stock_database
        _cache_timestamp = current_time

        logger.info(f"Tushare API总共获取到 {len(stock_database)} 只股票，已缓存")
        return stock_database

    except Exception as e:
        logger.warning(f"Tushare API调用失败: {e}")
        return {}

def get_akshare_us_stock_data():
    """从AkShare API获取美股股票数据（带缓存）"""
    global _akshare_us_stock_cache, _akshare_cache_timestamp

    try:
        import time
        current_time = time.time()

        # 检查缓存是否有效
        if (_akshare_us_stock_cache is not None and
            _akshare_cache_timestamp is not None and
            current_time - _akshare_cache_timestamp < _cache_duration):
            logger.info(f"使用缓存的AkShare美股数据，共 {len(_akshare_us_stock_cache)} 只股票")
            return _akshare_us_stock_cache

        import akshare as ak

        logger.info("正在从AkShare获取美股数据...")

        stock_database = {}

        try:
            # 使用AkShare获取美股实时行情数据
            us_stock_df = ak.stock_us_spot_em()

            if us_stock_df is not None and not us_stock_df.empty:
                logger.info(f"AkShare返回美股数据，形状: {us_stock_df.shape}")

                # 处理AkShare返回的美股数据
                # 通常包含 '代码', '名称' 等列
                for _, row in us_stock_df.iterrows():
                    try:
                        # 尝试不同的列名
                        symbol = None
                        name = None

                        # 可能的列名组合
                        symbol_cols = ['代码', 'symbol', 'Symbol', 'code', 'Code']
                        name_cols = ['名称', 'name', 'Name', '公司名称', 'company_name']

                        for col in symbol_cols:
                            if col in row and pd.notna(row[col]):
                                symbol = str(row[col]).strip().upper()
                                break

                        for col in name_cols:
                            if col in row and pd.notna(row[col]):
                                name = str(row[col]).strip()
                                break

                        if symbol and name and symbol != 'NAN' and name != 'NAN':
                            stock_database[symbol] = {
                                'name': name,
                                'market': '美股',
                                'industry': '未知'
                            }
                    except Exception as e:
                        # 跳过有问题的行
                        continue

                logger.info(f"从AkShare获取到 {len(stock_database)} 只美股数据")
            else:
                logger.warning("AkShare返回的美股数据为空")

        except Exception as e:
            logger.warning(f"AkShare stock_us_spot_em() 调用失败: {e}")

            # 尝试备用方法：使用硬编码的热门美股列表
            logger.info("使用备用美股数据...")
            backup_us_stocks = {
                'AAPL': {'name': 'Apple Inc.', 'market': '美股', 'industry': '科技'},
                'MSFT': {'name': 'Microsoft Corporation', 'market': '美股', 'industry': '科技'},
                'GOOGL': {'name': 'Alphabet Inc.', 'market': '美股', 'industry': '科技'},
                'AMZN': {'name': 'Amazon.com Inc.', 'market': '美股', 'industry': '电商'},
                'TSLA': {'name': 'Tesla Inc.', 'market': '美股', 'industry': '汽车'},
                'META': {'name': 'Meta Platforms Inc.', 'market': '美股', 'industry': '社交媒体'},
                'NVDA': {'name': 'NVIDIA Corporation', 'market': '美股', 'industry': 'AI芯片'},
                'NFLX': {'name': 'Netflix Inc.', 'market': '美股', 'industry': '流媒体'},
                'JPM': {'name': 'JPMorgan Chase & Co.', 'market': '美股', 'industry': '银行'},
                'JNJ': {'name': 'Johnson & Johnson', 'market': '美股', 'industry': '医药'},
                'V': {'name': 'Visa Inc.', 'market': '美股', 'industry': '金融服务'},
                'PG': {'name': 'Procter & Gamble Co.', 'market': '美股', 'industry': '日用品'},
                'UNH': {'name': 'UnitedHealth Group Inc.', 'market': '美股', 'industry': '医疗保险'},
                'HD': {'name': 'Home Depot Inc.', 'market': '美股', 'industry': '零售'},
                'MA': {'name': 'Mastercard Inc.', 'market': '美股', 'industry': '金融服务'},
                'BAC': {'name': 'Bank of America Corp.', 'market': '美股', 'industry': '银行'},
                'XOM': {'name': 'Exxon Mobil Corp.', 'market': '美股', 'industry': '石油'},
                'DIS': {'name': 'Walt Disney Co.', 'market': '美股', 'industry': '娱乐'},
                'CRM': {'name': 'Salesforce Inc.', 'market': '美股', 'industry': '企业软件'},
                'ADBE': {'name': 'Adobe Inc.', 'market': '美股', 'industry': '软件'},
            }
            stock_database.update(backup_us_stocks)
            logger.info(f"使用备用美股数据，共 {len(backup_us_stocks)} 只股票")

        # 更新缓存
        _akshare_us_stock_cache = stock_database
        _akshare_cache_timestamp = current_time

        return stock_database

    except Exception as e:
        logger.warning(f"AkShare美股数据获取失败: {e}")
        return {}

def get_stock_database_from_db():
    """从数据库获取股票信息"""
    try:
        from backend.data_manager import init_data_manager
        data_manager = init_data_manager()

        # 获取数据库连接
        with data_manager.db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT ts_code, symbol, name, market, industry FROM stock_info")
            records = cursor.fetchall()

            stock_database = {}
            for record in records:
                ts_code, symbol, name, market, industry = record
                stock_database[symbol] = {
                    'name': name or f'{symbol} 股票',
                    'market': market or 'Unknown',
                    'industry': industry or '未知'
                }

            logger.info(f"从数据库获取到 {len(stock_database)} 只股票信息")
            return stock_database

    except Exception as e:
        logger.warning(f"从数据库获取股票信息失败: {e}")
        return {}

def get_extended_stock_database():
    """获取扩展的股票数据库"""
    return {
        # 热门美股 - 科技股
        'AAPL': {'name': 'Apple Inc.', 'market': 'NASDAQ', 'industry': '科技'},
        'MSFT': {'name': 'Microsoft Corporation', 'market': 'NASDAQ', 'industry': '科技'},
        'GOOGL': {'name': 'Alphabet Inc.', 'market': 'NASDAQ', 'industry': '科技'},
        'GOOG': {'name': 'Alphabet Inc. Class C', 'market': 'NASDAQ', 'industry': '科技'},
        'AMZN': {'name': 'Amazon.com Inc.', 'market': 'NASDAQ', 'industry': '电商'},
        'TSLA': {'name': 'Tesla Inc.', 'market': 'NASDAQ', 'industry': '汽车'},
        'META': {'name': 'Meta Platforms Inc.', 'market': 'NASDAQ', 'industry': '社交媒体'},
        'NVDA': {'name': 'NVIDIA Corporation', 'market': 'NASDAQ', 'industry': 'AI芯片'},
        'NFLX': {'name': 'Netflix Inc.', 'market': 'NASDAQ', 'industry': '流媒体'},
        'ADBE': {'name': 'Adobe Inc.', 'market': 'NASDAQ', 'industry': '软件'},
        'CRM': {'name': 'Salesforce Inc.', 'market': 'NYSE', 'industry': '企业软件'},
        'ORCL': {'name': 'Oracle Corporation', 'market': 'NYSE', 'industry': '软件'},
        'INTC': {'name': 'Intel Corporation', 'market': 'NASDAQ', 'industry': '半导体'},
        'AMD': {'name': 'Advanced Micro Devices', 'market': 'NASDAQ', 'industry': '半导体'},
        'QCOM': {'name': 'Qualcomm Inc.', 'market': 'NASDAQ', 'industry': '半导体'},
        'AVGO': {'name': 'Broadcom Inc.', 'market': 'NASDAQ', 'industry': '半导体'},
        'TXN': {'name': 'Texas Instruments', 'market': 'NASDAQ', 'industry': '半导体'},
        'CSCO': {'name': 'Cisco Systems Inc.', 'market': 'NASDAQ', 'industry': '网络设备'},
        'IBM': {'name': 'International Business Machines', 'market': 'NYSE', 'industry': '科技服务'},
        'UBER': {'name': 'Uber Technologies Inc.', 'market': 'NYSE', 'industry': '出行服务'},
        'LYFT': {'name': 'Lyft Inc.', 'market': 'NASDAQ', 'industry': '出行服务'},
        'ZOOM': {'name': 'Zoom Video Communications', 'market': 'NASDAQ', 'industry': '视频会议'},
        'SHOP': {'name': 'Shopify Inc.', 'market': 'NYSE', 'industry': '电商平台'},
        'SQ': {'name': 'Block Inc.', 'market': 'NYSE', 'industry': '金融科技'},
        'PYPL': {'name': 'PayPal Holdings Inc.', 'market': 'NASDAQ', 'industry': '支付'},

        # 金融股
        'JPM': {'name': 'JPMorgan Chase & Co.', 'market': 'NYSE', 'industry': '银行'},
        'BAC': {'name': 'Bank of America Corp.', 'market': 'NYSE', 'industry': '银行'},
        'WFC': {'name': 'Wells Fargo & Co.', 'market': 'NYSE', 'industry': '银行'},
        'C': {'name': 'Citigroup Inc.', 'market': 'NYSE', 'industry': '银行'},
        'GS': {'name': 'Goldman Sachs Group Inc.', 'market': 'NYSE', 'industry': '投资银行'},
        'MS': {'name': 'Morgan Stanley', 'market': 'NYSE', 'industry': '投资银行'},
        'V': {'name': 'Visa Inc.', 'market': 'NYSE', 'industry': '金融服务'},
        'MA': {'name': 'Mastercard Inc.', 'market': 'NYSE', 'industry': '金融服务'},
        'AXP': {'name': 'American Express Co.', 'market': 'NYSE', 'industry': '金融服务'},
        'BRK.A': {'name': 'Berkshire Hathaway Inc.', 'market': 'NYSE', 'industry': '投资'},
        'BRK.B': {'name': 'Berkshire Hathaway Inc. Class B', 'market': 'NYSE', 'industry': '投资'},

        # 医药健康
        'JNJ': {'name': 'Johnson & Johnson', 'market': 'NYSE', 'industry': '医药'},
        'PFE': {'name': 'Pfizer Inc.', 'market': 'NYSE', 'industry': '医药'},
        'UNH': {'name': 'UnitedHealth Group Inc.', 'market': 'NYSE', 'industry': '医疗保险'},
        'ABBV': {'name': 'AbbVie Inc.', 'market': 'NYSE', 'industry': '生物医药'},
        'TMO': {'name': 'Thermo Fisher Scientific', 'market': 'NYSE', 'industry': '生命科学'},
        'ABT': {'name': 'Abbott Laboratories', 'market': 'NYSE', 'industry': '医疗器械'},
        'LLY': {'name': 'Eli Lilly and Co.', 'market': 'NYSE', 'industry': '医药'},
        'BMY': {'name': 'Bristol Myers Squibb', 'market': 'NYSE', 'industry': '医药'},
        'MRK': {'name': 'Merck & Co. Inc.', 'market': 'NYSE', 'industry': '医药'},
        'GILD': {'name': 'Gilead Sciences Inc.', 'market': 'NASDAQ', 'industry': '生物医药'},

        # 消费品
        'PG': {'name': 'Procter & Gamble Co.', 'market': 'NYSE', 'industry': '日用品'},
        'KO': {'name': 'Coca-Cola Co.', 'market': 'NYSE', 'industry': '饮料'},
        'PEP': {'name': 'PepsiCo Inc.', 'market': 'NASDAQ', 'industry': '饮料'},
        'WMT': {'name': 'Walmart Inc.', 'market': 'NYSE', 'industry': '零售'},
        'HD': {'name': 'Home Depot Inc.', 'market': 'NYSE', 'industry': '零售'},
        'MCD': {'name': 'McDonald\'s Corp.', 'market': 'NYSE', 'industry': '餐饮'},
        'SBUX': {'name': 'Starbucks Corp.', 'market': 'NASDAQ', 'industry': '餐饮'},
        'NKE': {'name': 'Nike Inc.', 'market': 'NYSE', 'industry': '体育用品'},
        'DIS': {'name': 'Walt Disney Co.', 'market': 'NYSE', 'industry': '娱乐'},
        'COST': {'name': 'Costco Wholesale Corp.', 'market': 'NASDAQ', 'industry': '零售'},

        # 能源
        'XOM': {'name': 'Exxon Mobil Corp.', 'market': 'NYSE', 'industry': '石油'},
        'CVX': {'name': 'Chevron Corp.', 'market': 'NYSE', 'industry': '石油'},
        'COP': {'name': 'ConocoPhillips', 'market': 'NYSE', 'industry': '石油'},
        'SLB': {'name': 'Schlumberger Ltd.', 'market': 'NYSE', 'industry': '油服'},

        # 热门港股
        '00700.HK': {'name': '腾讯控股', 'market': 'HKEX', 'industry': '互联网'},
        '00941.HK': {'name': '中国移动', 'market': 'HKEX', 'industry': '通信'},
        '03690.HK': {'name': '美团-W', 'market': 'HKEX', 'industry': '生活服务'},
        '00175.HK': {'name': '吉利汽车', 'market': 'HKEX', 'industry': '汽车'},
        '09988.HK': {'name': '阿里巴巴-SW', 'market': 'HKEX', 'industry': '电商'},
        '02318.HK': {'name': '中国平安', 'market': 'HKEX', 'industry': '保险'},
        '01398.HK': {'name': '工商银行', 'market': 'HKEX', 'industry': '银行'},
        '00939.HK': {'name': '建设银行', 'market': 'HKEX', 'industry': '银行'},
        '01299.HK': {'name': '友邦保险', 'market': 'HKEX', 'industry': '保险'},
        '02020.HK': {'name': '安踏体育', 'market': 'HKEX', 'industry': '体育用品'},
        '01024.HK': {'name': '快手-W', 'market': 'HKEX', 'industry': '短视频'},
        '09618.HK': {'name': '京东集团-SW', 'market': 'HKEX', 'industry': '电商'},
        '01810.HK': {'name': '小米集团-W', 'market': 'HKEX', 'industry': '智能手机'},
        '09999.HK': {'name': '网易-S', 'market': 'HKEX', 'industry': '游戏'},
        '02269.HK': {'name': '药明生物', 'market': 'HKEX', 'industry': '生物医药'},
        '01211.HK': {'name': '比亚迪股份', 'market': 'HKEX', 'industry': '新能源汽车'},
        '02015.HK': {'name': '理想汽车-W', 'market': 'HKEX', 'industry': '新能源汽车'},
        '09868.HK': {'name': '小鹏汽车-W', 'market': 'HKEX', 'industry': '新能源汽车'},
        '09961.HK': {'name': '蔚来-SW', 'market': 'HKEX', 'industry': '新能源汽车'},
        '06618.HK': {'name': '京东健康', 'market': 'HKEX', 'industry': '医疗健康'},
        '03888.HK': {'name': '金山软件', 'market': 'HKEX', 'industry': '软件'},
        '01833.HK': {'name': '平安好医生', 'market': 'HKEX', 'industry': '医疗健康'},

        # 热门A股
        '000001.SZ': {'name': '平安银行', 'market': 'SZSE', 'industry': '银行'},
        '600519.SH': {'name': '贵州茅台', 'market': 'SSE', 'industry': '白酒'},
        '000858.SZ': {'name': '五粮液', 'market': 'SZSE', 'industry': '白酒'},
        '600036.SH': {'name': '招商银行', 'market': 'SSE', 'industry': '银行'},
        '000002.SZ': {'name': '万科A', 'market': 'SZSE', 'industry': '房地产'},
        '601318.SH': {'name': '中国平安', 'market': 'SSE', 'industry': '保险'},
        '600276.SH': {'name': '恒瑞医药', 'market': 'SSE', 'industry': '医药'},
        '000568.SZ': {'name': '泸州老窖', 'market': 'SZSE', 'industry': '白酒'},
        '002415.SZ': {'name': '海康威视', 'market': 'SZSE', 'industry': '安防'},
        '601166.SH': {'name': '兴业银行', 'market': 'SSE', 'industry': '银行'},
        '600887.SH': {'name': '伊利股份', 'market': 'SSE', 'industry': '食品'},
        '002594.SZ': {'name': 'BYD比亚迪', 'market': 'SZSE', 'industry': '新能源汽车'},
        '300750.SZ': {'name': '宁德时代', 'market': 'SZSE', 'industry': '新能源'},
        '601012.SH': {'name': '隆基绿能', 'market': 'SSE', 'industry': '光伏'},
        '000725.SZ': {'name': '京东方A', 'market': 'SZSE', 'industry': '显示面板'},
        '002230.SZ': {'name': '科大讯飞', 'market': 'SZSE', 'industry': '人工智能'},
        '000063.SZ': {'name': '中兴通讯', 'market': 'SZSE', 'industry': '通信设备'},
        '600030.SH': {'name': '中信证券', 'market': 'SSE', 'industry': '证券'},
        '000776.SZ': {'name': '广发证券', 'market': 'SZSE', 'industry': '证券'},
        '601888.SH': {'name': '中国中免', 'market': 'SSE', 'industry': '免税'},
        '600009.SH': {'name': '上海机场', 'market': 'SSE', 'industry': '机场'},
        '000100.SZ': {'name': 'TCL科技', 'market': 'SZSE', 'industry': '电子'},
        '002142.SZ': {'name': '宁波银行', 'market': 'SZSE', 'industry': '银行'},
        '600000.SH': {'name': '浦发银行', 'market': 'SSE', 'industry': '银行'},
        '601328.SH': {'name': '交通银行', 'market': 'SSE', 'industry': '银行'},
        '601288.SH': {'name': '农业银行', 'market': 'SSE', 'industry': '银行'},
        '601939.SH': {'name': '建设银行', 'market': 'SSE', 'industry': '银行'},
        '601398.SH': {'name': '工商银行', 'market': 'SSE', 'industry': '银行'},
        '000858.SZ': {'name': '五粮液', 'market': 'SZSE', 'industry': '白酒'},
        '002304.SZ': {'name': '洋河股份', 'market': 'SZSE', 'industry': '白酒'},
        '000596.SZ': {'name': '古井贡酒', 'market': 'SZSE', 'industry': '白酒'},
        '603288.SH': {'name': '海天味业', 'market': 'SSE', 'industry': '调味品'},
        '000895.SZ': {'name': '双汇发展', 'market': 'SZSE', 'industry': '食品'},
        '600298.SH': {'name': '安琪酵母', 'market': 'SSE', 'industry': '生物制品'},
        '300015.SZ': {'name': '爱尔眼科', 'market': 'SZSE', 'industry': '医疗服务'},
        '000661.SZ': {'name': '长春高新', 'market': 'SZSE', 'industry': '生物医药'},
        '300760.SZ': {'name': '迈瑞医疗', 'market': 'SZSE', 'industry': '医疗器械'},
    }

@app.get("/stocks/search/{query}")
async def search_stocks(query: str):
    """搜索股票（模糊匹配）- 使用新的元数据管理器"""
    try:
        from backend.stock_metadata_manager import get_metadata_manager

        suggestions = []
        query_upper = query.upper()
        query_lower = query.lower()

        # 使用新的元数据管理器获取股票数据
        metadata_manager = get_metadata_manager()
        stock_database = metadata_manager.get_cached_stock_list()

        # 如果元数据管理器没有数据，回退到原有方法
        if not stock_database:
            logger.warning("元数据管理器中没有数据，回退到原有方法")
            # 优先尝试从Tushare API获取实时股票数据
            stock_database = get_tushare_stock_data()

            # 补充AkShare美股数据
            akshare_us_stocks = get_akshare_us_stock_data()
            if akshare_us_stocks:
                logger.info(f"从AkShare获取到 {len(akshare_us_stocks)} 只美股数据，合并到股票数据库")
                stock_database.update(akshare_us_stocks)

            # 如果Tushare API失败，尝试从数据库获取
            if len(stock_database) < 100:
                logger.info(f"当前股票数量较少({len(stock_database)})，尝试从数据库获取")
                db_stocks = get_stock_database_from_db()
                stock_database.update(db_stocks)

        # 如果数据库也为空或股票数量太少，使用扩展的硬编码数据库
        if len(stock_database) < 50:
            logger.info(f"数据库中股票数量较少({len(stock_database)})，使用扩展的硬编码股票数据库")
            extended_database = get_extended_stock_database()
            # 合并数据库和硬编码数据
            stock_database.update(extended_database)

        logger.info(f"使用股票数据库，共 {len(stock_database)} 只股票")
        
        # 智能搜索逻辑
        for symbol, info in stock_database.items():
            score = 0
            
            # 完全匹配股票代码（最高优先级）
            if query_upper == symbol.upper():
                score = 100
            # 代码包含查询词
            elif query_upper in symbol.upper():
                score = 80
            # 公司名称完全匹配
            elif query_lower == info['name'].lower():
                score = 90
            # 公司名称包含查询词
            elif query_lower in info['name'].lower():
                score = 60
            # 行业匹配
            elif query_lower in info['industry'].lower():
                score = 40
            # 模糊匹配（拼音等）
            elif any(char in info['name'] for char in query if len(char) > 0):
                score = 20
            
            if score > 0:
                suggestions.append({
                    'symbol': symbol,
                    'name': info['name'],
                    'market': info['market'],
                    'industry': info['industry'],
                    'score': score
                })
        
        # 按评分排序并限制结果数量
        suggestions.sort(key=lambda x: x['score'], reverse=True)
        suggestions = suggestions[:15]
        
        # 移除评分字段
        for suggestion in suggestions:
            suggestion.pop('score', None)
        
        return {
            'success': True,
            'query': query,
            'suggestions': suggestions,
            'count': len(suggestions),
            'timestamp': datetime.datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"搜索股票错误: {e}")
        raise HTTPException(status_code=500, detail=f"搜索股票错误: {str(e)}")


# Stock suggestions endpoint (alias for suggestions)
@app.get("/stocks/suggestions/{query}")
async def get_stock_suggestions(query: str, limit: int = 10):
    """获取股票建议（搜索建议）"""
    try:
        # 复用搜索功能
        result = await search_stocks(query)
        
        # 限制结果数量
        suggestions = result['suggestions'][:limit]
        
        return {
            'success': True,
            'query': query,
            'suggestions': suggestions,
            'count': len(suggestions),
            'limit': limit,
            'timestamp': datetime.datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取股票建议错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取股票建议错误: {str(e)}")


# News endpoints
class NewsRequest(BaseModel):
    symbol: str
    include_general_news: Optional[bool] = True
    date_for_reports: Optional[str] = None
    max_rows_per_source: Optional[int] = 30

class NewsSearchRequest(BaseModel):
    keyword: str
    max_rows: Optional[int] = 50
    use_tushare: Optional[bool] = False
    tushare_source: Optional[str] = 'sina'  # 支持: sina, fenghuang, jinrongjie, 10jqka, yuncaijing, eastmoney, wallstreetcn
    tushare_token: Optional[str] = None

class LiveNewsRequest(BaseModel):
    sources: Optional[str] = ""  # 新闻源列表，用逗号分隔，留空获取所有源
    max_rows_per_source: Optional[int] = 10
    hours_back: Optional[int] = 6  # 获取最近几小时的新闻
    max_total_rows: Optional[int] = 50


@app.post("/stocks/news")
async def get_stock_news(request: NewsRequest):
    """获取股票新闻"""
    try:
        # Import akshare tools
        from backend.ai.tools.akshare.stock_news import comprehensive_stock_news_tool

        # Prepare parameters for the comprehensive news tool
        params = {
            "symbol": request.symbol,
            "include_general_news": request.include_general_news,
            "max_rows_per_source": request.max_rows_per_source
        }

        if request.date_for_reports:
            params["date_for_reports"] = request.date_for_reports

        # Call the comprehensive stock news tool
        result = comprehensive_stock_news_tool.invoke(params)

        # Parse the JSON result
        import json
        news_data = json.loads(result)

        return {
            'success': True,
            'symbol': request.symbol,
            'news_data': news_data,
            'timestamp': datetime.datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"获取股票新闻错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取股票新闻错误: {str(e)}")


@app.options("/news/search")
async def search_news_options():
    """处理新闻搜索的CORS预检请求"""
    return {"message": "OK"}

@app.post("/news/search")
async def search_news(request: NewsSearchRequest):
    """搜索新闻资讯"""
    try:
        # 导入URL工具函数
        from backend.utils.url_utils import is_tushare_news_url, extract_keyword_from_tushare_url
        
        # 检测是否为Tushare URL格式
        is_tushare_format = is_tushare_news_url(request.keyword)
        
        # 所有搜索都使用Tushare真实数据，不使用模拟数据
        try:
            from backend.ai.tools.tushare.news_search import tushare_news_search_tool
            
            # 确定新闻源
            news_source = request.tushare_source or 'sina'
            
            # 如果是Tushare URL格式，保持原有逻辑
            if is_tushare_format:
                logger.info(f"使用Tushare URL格式搜索: {request.keyword}")
            else:
                logger.info(f"使用关键词进行Tushare新闻搜索: {request.keyword}")
            
            # 调用Tushare新闻搜索工具
            result = tushare_news_search_tool.invoke({
                "keyword": request.keyword,
                "source": news_source,
                "max_rows": request.max_rows or 50
            })
            
            # 解析JSON结果
            import json
            tushare_data = json.loads(result)
            
            # 转换为统一格式
            transformed_data = {
                "keyword": tushare_data.get("keyword", request.keyword),
                "timestamp": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "data_sources": {
                    "tushare_news": {
                        "source": tushare_data.get("source", f"Tushare Pro - {news_source}"),
                        "total_rows": tushare_data.get("total_rows", 0),
                        "displayed_rows": tushare_data.get("displayed_rows", 0),
                        "data": tushare_data.get("data", []),
                        "filtered_rows": tushare_data.get("filtered_rows", 0),
                        "note": f"真实数据来源：Tushare Pro - {news_source}"
                    }
                }
            }
            
            # 如果有错误，处理错误情况
            if "error" in tushare_data:
                transformed_data["data_sources"]["tushare_news"]["error"] = tushare_data["error"]
                transformed_data["data_sources"]["tushare_news"]["message"] = tushare_data.get("message", "")
                # 即使有错误，也不使用模拟数据，返回错误信息
                logger.warning(f"Tushare返回错误: {tushare_data.get('error')}")
            
            return {
                'success': True,
                'keyword': request.keyword,
                'news_data': transformed_data,
                'timestamp': datetime.datetime.now().isoformat(),
                'source_type': 'tushare'
            }
            
        except Exception as tushare_error:
            logger.error(f"Tushare新闻搜索失败: {tushare_error}")
            # 不使用模拟数据回退，直接返回错误
            raise HTTPException(
                status_code=500, 
                detail=f"Tushare新闻搜索失败: {str(tushare_error)}。请检查Tushare Token配置或网络连接。"
            )

    except Exception as e:
        logger.error(f"搜索新闻错误: {e}")
        raise HTTPException(status_code=500, detail=f"搜索新闻错误: {str(e)}")


@app.options("/news/live")
async def live_news_options():
    """处理实时新闻的CORS预检请求"""
    return {"message": "OK"}

@app.post("/news/live")
async def get_live_news(request: LiveNewsRequest):
    """获取实时新闻数据"""
    try:
        # 导入AkShare实时新闻工具（替代Tushare）
        from backend.ai.tools.akshare.live_news import akshare_live_news_tool
        
        # 调用AkShare实时新闻工具
        result = akshare_live_news_tool.invoke({
            "sources": request.sources or "",
            "max_rows_per_source": request.max_rows_per_source or 10,
            "hours_back": request.hours_back or 6,
            "max_total_rows": request.max_total_rows or 50
        })
        
        # 解析JSON结果
        import json
        live_data = json.loads(result)
        
        # 如果有错误，处理错误情况
        if "error" in live_data:
            return {
                'success': False,
                'error': live_data["error"],
                'message': live_data.get("message", ""),
                'timestamp': datetime.datetime.now().isoformat()
            }
        
        # 转换为统一格式
        transformed_data = {
            "timestamp": live_data.get("timestamp"),
            "sources_requested": live_data.get("sources_requested", []),
            "sources_stats": live_data.get("sources_stats", {}),
            "total_news": live_data.get("total_news", 0),
            "time_range_hours": live_data.get("time_range_hours", 6),
            "data_sources": {
                "live_news": {
                    "source": "AkShare - 实时新闻聚合",
                    "total_rows": live_data.get("total_news", 0),
                    "displayed_rows": live_data.get("total_news", 0),
                    "data": live_data.get("data", []),
                    "sources_stats": live_data.get("sources_stats", {}),
                    "note": "实时聚合多个新闻源的最新新闻，无API限制"
                }
            }
        }
        
        return {
            'success': True,
            'live_data': transformed_data,
            'timestamp': datetime.datetime.now().isoformat(),
            'source_type': 'live'
        }
        
    except Exception as e:
        logger.error(f"获取实时新闻错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取实时新闻错误: {str(e)}")


@app.get("/news/sources/summary")
async def get_news_sources_summary():
    """获取新闻源状态摘要"""
    try:
        from backend.ai.tools.akshare.live_news import akshare_news_summary_tool
        
        result = akshare_news_summary_tool.invoke({
            "hours_back": 24
        })
        
        import json
        summary_data = json.loads(result)
        
        return {
            'success': True,
            'summary': summary_data,
            'timestamp': datetime.datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取新闻源摘要错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取新闻源摘要错误: {str(e)}")


# ==================== 新增：基于数据库的财经新闻API ====================

class FinancialNewsQueryRequest(BaseModel):
    sources: Optional[List[str]] = None  # 指定数据源列表
    limit: Optional[int] = 50  # 限制数量
    offset: Optional[int] = 0  # 偏移量
    start_time: Optional[str] = None  # 开始时间 YYYY-MM-DD HH:MM:SS
    end_time: Optional[str] = None  # 结束时间 YYYY-MM-DD HH:MM:SS
    keyword: Optional[str] = None  # 搜索关键词


class FinancialNewsSyncRequest(BaseModel):
    sync_type: str  # 'breakfast', 'global', 'comprehensive', 'all'
    sources: Optional[str] = ""  # 指定数据源，用逗号分隔
    max_workers: Optional[int] = 3  # 并发线程数


@app.get("/financial-news/latest")
async def get_latest_financial_news(
    limit: int = 50,
    sources: Optional[str] = None
):
    """获取最新财经新闻"""
    try:
        from backend.core.data.managers.financial_news_manager import get_financial_news_manager
        
        news_manager = get_financial_news_manager()
        
        # 解析数据源参数
        source_list = None
        if sources:
            source_list = [s.strip() for s in sources.split(',') if s.strip()]
        
        # 获取最新新闻
        news_data = news_manager.get_latest_news(limit=limit, sources=source_list)
        
        return {
            'success': True,
            'count': len(news_data),
            'data': news_data,
            'timestamp': datetime.datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取最新财经新闻错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取最新财经新闻错误: {str(e)}")


@app.post("/financial-news/query")
async def query_financial_news(request: FinancialNewsQueryRequest):
    """查询财经新闻"""
    try:
        from backend.core.data.managers.financial_news_manager import get_financial_news_manager
        from datetime import datetime
        
        news_manager = get_financial_news_manager()
        
        # 根据查询类型调用不同方法
        if request.keyword:
            # 关键词搜索
            news_data = news_manager.search_news(
                keyword=request.keyword,
                sources=request.sources,
                limit=request.limit or 100
            )
        elif request.start_time and request.end_time:
            # 时间范围查询
            start_time = datetime.fromisoformat(request.start_time)
            end_time = datetime.fromisoformat(request.end_time)
            news_data = news_manager.get_news_by_timerange(
                start_time=start_time,
                end_time=end_time,
                sources=request.sources,
                limit=request.limit or 200
            )
        else:
            # 获取最新新闻
            news_data = news_manager.get_latest_news(
                limit=request.limit or 50,
                sources=request.sources
            )
        
        return {
            'success': True,
            'count': len(news_data),
            'data': news_data,
            'query_params': {
                'sources': request.sources,
                'limit': request.limit,
                'offset': request.offset,
                'start_time': request.start_time,
                'end_time': request.end_time,
                'keyword': request.keyword
            },
            'timestamp': datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"查询财经新闻错误: {e}")
        raise HTTPException(status_code=500, detail=f"查询财经新闻错误: {str(e)}")


@app.get("/financial-news/sources")
async def get_financial_news_sources():
    """获取财经新闻数据源状态"""
    try:
        from backend.core.data.managers.financial_news_manager import get_financial_news_manager
        
        news_manager = get_financial_news_manager()
        
        # 获取数据源状态
        source_status = news_manager.get_source_status()
        
        # 获取统计信息
        statistics = news_manager.get_statistics()
        
        return {
            'success': True,
            'sources': source_status,
            'statistics': statistics,
            'timestamp': datetime.datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取财经新闻数据源状态错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取财经新闻数据源状态错误: {str(e)}")


@app.post("/financial-news/sync")
async def sync_financial_news(request: FinancialNewsSyncRequest):
    """手动同步财经新闻数据"""
    try:
        from backend.ai.tools.akshare.financial_news import (
            akshare_financial_breakfast_tool,
            akshare_financial_global_news_tool,
            akshare_financial_comprehensive_tool
        )
        import json
        
        if request.sync_type == 'breakfast':
            # 同步财经早餐
            result = akshare_financial_breakfast_tool.invoke({})
            
        elif request.sync_type == 'global':
            # 同步全球财经快讯
            result = akshare_financial_global_news_tool.invoke({
                'sources': request.sources or '',
                'max_workers': request.max_workers or 3
            })
            
        elif request.sync_type == 'comprehensive':
            # 同步综合财经资讯
            result = akshare_financial_comprehensive_tool.invoke({
                'include_breakfast': True,
                'include_global': True,
                'include_broker': False,
                'max_workers': request.max_workers or 5
            })
            
        elif request.sync_type == 'all':
            # 同步所有数据
            result = akshare_financial_comprehensive_tool.invoke({
                'include_breakfast': True,
                'include_global': True,
                'include_broker': True,
                'max_workers': request.max_workers or 5
            })
            
        else:
            raise HTTPException(status_code=400, detail=f"不支持的同步类型: {request.sync_type}")
        
        # 解析结果
        sync_data = json.loads(result)
        
        return {
            'success': True,
            'sync_type': request.sync_type,
            'result': sync_data,
            'timestamp': datetime.datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"同步财经新闻数据错误: {e}")
        raise HTTPException(status_code=500, detail=f"同步财经新闻数据错误: {str(e)}")


@app.get("/financial-news/scheduler/status")
async def get_news_scheduler_status():
    """获取新闻同步调度器状态"""
    try:
        from backend.services.tasks.news_sync_scheduler import get_news_scheduler
        
        scheduler = get_news_scheduler()
        status = scheduler.get_scheduler_status()
        
        return {
            'success': True,
            'scheduler_status': status,
            'timestamp': datetime.datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取调度器状态错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取调度器状态错误: {str(e)}")


@app.post("/financial-news/scheduler/trigger/{job_id}")
async def trigger_news_sync_job(job_id: str):
    """手动触发新闻同步任务"""
    try:
        from backend.services.tasks.news_sync_scheduler import get_news_scheduler
        
        scheduler = get_news_scheduler()
        result = await scheduler.trigger_job(job_id)
        
        return {
            'success': 'success' in result,
            'job_id': job_id,
            'result': result,
            'timestamp': datetime.datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"触发同步任务错误: {e}")
        raise HTTPException(status_code=500, detail=f"触发同步任务错误: {str(e)}")


@app.get("/financial-news/statistics")
async def get_financial_news_statistics():
    """获取财经新闻数据统计"""
    try:
        from backend.core.data.managers.financial_news_manager import get_financial_news_manager

        news_manager = get_financial_news_manager()
        statistics = news_manager.get_statistics()

        return {
            'success': True,
            'statistics': statistics,
            'timestamp': datetime.datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"获取财经新闻统计错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取财经新闻统计错误: {str(e)}")


@app.get("/supabase-sync/status")
async def get_supabase_sync_status():
    """获取Supabase同步服务状态"""
    try:
        from backend.services.tasks.local_to_supabase_sync import get_supabase_sync_service

        sync_service = get_supabase_sync_service()
        status = sync_service.get_sync_status()

        return {
            'success': True,
            'sync_status': status,
            'timestamp': datetime.datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"获取Supabase同步状态错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取Supabase同步状态错误: {str(e)}")


@app.post("/supabase-sync/trigger")
async def trigger_supabase_sync(hours_back: int = 24):
    """手动触发Supabase同步"""
    try:
        from backend.services.tasks.local_to_supabase_sync import get_supabase_sync_service

        sync_service = get_supabase_sync_service()
        result = await sync_service.sync_recent_news(hours_back=hours_back)

        return {
            'success': result.get('status') in ['success', 'no_data', 'no_valid_data'],
            'sync_result': result,
            'timestamp': datetime.datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"手动触发Supabase同步错误: {e}")
        raise HTTPException(status_code=500, detail=f"手动触发Supabase同步错误: {str(e)}")


# ==================== 新闻影响分析 API ====================

class NewsImpactAnalysisRequest(BaseModel):
    news_id: Optional[int] = None  # 新闻ID（从数据库获取）
    news_title: Optional[str] = None  # 新闻标题
    news_content: Optional[str] = None  # 新闻内容
    news_source: Optional[str] = None  # 新闻来源
    news_publish_time: Optional[str] = None  # 发布时间
    model: Optional[str] = 'glm'  # 分析模型选择：'glm' 或 'gemini'


class BatchNewsAnalysisRequest(BaseModel):
    news_list: List[Dict[str, Any]]  # 新闻列表
    max_concurrent: Optional[int] = 3  # 最大并发数


@app.post("/news/impact-analysis")
async def analyze_news_impact(request: NewsImpactAnalysisRequest):
    """分析单条新闻的市场影响"""
    try:
        from backend.services.news.news_impact_analyzer import get_news_impact_analyzer
        
        analyzer = get_news_impact_analyzer()
        
        # 检查选择的模型是否可用
        if request.model == 'gemini':
            if not analyzer.llm_manager.is_available():
                raise HTTPException(
                    status_code=503, 
                    detail="Gemini API不可用，请检查GEMINI_API_KEY环境变量设置"
                )
        else:  # GLM
            if not analyzer.glm_client.is_available():
                raise HTTPException(
                    status_code=503, 
                    detail="GLM API不可用，请检查GLM_API_KEY环境变量设置"
                )
        
        # 构建新闻数据
        if request.news_id:
            # 从数据库获取新闻
            from backend.core.data.managers.financial_news_manager import get_financial_news_manager
            news_manager = get_financial_news_manager()
            news_data = news_manager.get_news_by_id(request.news_id)
            
            if not news_data:
                raise HTTPException(status_code=404, detail="新闻不存在")
                
        else:
            # 使用提供的新闻数据
            if not request.news_title or not request.news_content:
                raise HTTPException(status_code=400, detail="新闻标题和内容不能为空")
                
            news_data = {
                'title': request.news_title,
                'content': request.news_content,
                'source': request.news_source or 'manual',
                'publish_time': request.news_publish_time or datetime.datetime.now().isoformat()
            }
        
        # 执行分析
        result = await analyzer.analyze_news(news_data, model=request.model)
        
        return {
            'success': result.get('success', False),
            'analysis': result.get('analysis'),
            'cached': result.get('cached', False),
            'analysis_id': result.get('analysis_id'),
            'error': result.get('error'),
            'timestamp': datetime.datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"新闻影响分析错误: {e}")
        raise HTTPException(status_code=500, detail=f"新闻影响分析错误: {str(e)}")


@app.post("/news/batch-impact-analysis")
async def batch_analyze_news_impact(request: BatchNewsAnalysisRequest):
    """批量分析新闻的市场影响"""
    try:
        from backend.services.news.news_impact_analyzer import get_news_impact_analyzer
        
        analyzer = get_news_impact_analyzer()
        
        # 检查GLM API是否可用
        if not analyzer.glm_client.is_available():
            raise HTTPException(
                status_code=503, 
                detail="GLM API不可用，请检查GLM_API_KEY环境变量设置"
            )
        
        if not request.news_list:
            raise HTTPException(status_code=400, detail="新闻列表不能为空")
        
        # 执行批量分析
        results = await analyzer.batch_analyze_news(
            request.news_list, 
            request.max_concurrent or 3
        )
        
        # 统计结果
        success_count = sum(1 for r in results if r.get('success'))
        error_count = len(results) - success_count
        
        return {
            'success': True,
            'total_count': len(results),
            'success_count': success_count,
            'error_count': error_count,
            'results': results,
            'timestamp': datetime.datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量新闻影响分析错误: {e}")
        raise HTTPException(status_code=500, detail=f"批量新闻影响分析错误: {str(e)}")


@app.get("/news/impact-analysis/{analysis_id}")
async def get_news_impact_analysis(analysis_id: int):
    """获取新闻影响分析结果"""
    try:
        from backend.services.news.news_impact_analyzer import get_news_impact_analyzer
        
        analyzer = get_news_impact_analyzer()
        
        # 获取分析结果
        analysis = analyzer.get_analysis_by_id(analysis_id)
        
        if not analysis:
            raise HTTPException(status_code=404, detail="分析结果不存在")
        
        return {
            'success': True,
            'analysis': analysis,
            'timestamp': datetime.datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取新闻影响分析结果错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取新闻影响分析结果错误: {str(e)}")


@app.get("/news/impact-analysis/recent")
async def get_recent_impact_analyses(
    limit: int = 20,
    impact_level: Optional[str] = None
):
    """获取最近的新闻影响分析结果"""
    try:
        from backend.services.news.news_impact_analyzer import get_news_impact_analyzer
        
        analyzer = get_news_impact_analyzer()
        
        # 获取最近的分析结果
        analyses = analyzer.get_recent_analyses(limit=limit, impact_level=impact_level)
        
        return {
            'success': True,
            'count': len(analyses),
            'data': analyses,
            'timestamp': datetime.datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取最近新闻影响分析结果错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取最近新闻影响分析结果错误: {str(e)}")


@app.get("/news/impact-analysis/statistics")
async def get_impact_analysis_statistics():
    """获取新闻影响分析统计信息"""
    try:
        from backend.services.news.news_impact_analyzer import get_news_impact_analyzer
        
        analyzer = get_news_impact_analyzer()
        
        # 获取统计信息
        statistics = analyzer.get_analysis_statistics()
        
        return {
            'success': True,
            'statistics': statistics,
            'timestamp': datetime.datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取新闻影响分析统计信息错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取新闻影响分析统计信息错误: {str(e)}")


@app.post("/news/impact-analysis/auto-analyze-latest")
async def auto_analyze_latest_news(
    limit: int = 10,
    hours_back: int = 6,
    impact_threshold: str = "中"
):
    """自动分析最新的重要新闻"""
    try:
        from backend.core.data.managers.financial_news_manager import get_financial_news_manager
        from backend.services.news.news_impact_analyzer import get_news_impact_analyzer
        from datetime import datetime, timedelta
        
        news_manager = get_financial_news_manager()
        analyzer = get_news_impact_analyzer()
        
        # 检查GLM API是否可用
        if not analyzer.glm_client.is_available():
            raise HTTPException(
                status_code=503, 
                detail="GLM API不可用，请检查GLM_API_KEY环境变量设置"
            )
        
        # 获取最新新闻
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=hours_back)
        
        latest_news = news_manager.get_news_by_timerange(
            start_time=start_time,
            end_time=end_time,
            limit=limit
        )
        
        if not latest_news:
            return {
                'success': True,
                'message': '没有找到最新新闻',
                'analyzed_count': 0,
                'results': [],
                'timestamp': datetime.now().isoformat()
            }
        
        # 过滤出重要新闻（标题长度、关键词等）
        important_news = []
        keywords = ['伊朗', '以色列', '战争', '冲突', '制裁', '央行', '利率', '通胀', 'GDP', '贸易战', '股市', '暴跌', '暴涨']
        
        for news in latest_news:
            title = news.get('title', '')
            content = news.get('content', '')
            
            # 简单的重要性判断
            if (len(title) > 20 or 
                any(keyword in title for keyword in keywords) or
                any(keyword in content for keyword in keywords)):
                important_news.append(news)
        
        if not important_news:
            return {
                'success': True,
                'message': '没有找到重要新闻',
                'analyzed_count': 0,
                'results': [],
                'timestamp': datetime.now().isoformat()
            }
        
        # 批量分析
        results = await analyzer.batch_analyze_news(important_news[:limit], max_concurrent=3)
        
        # 过滤高影响的分析结果
        high_impact_results = []
        for result in results:
            if result.get('success') and result.get('analysis'):
                analysis = result['analysis']
                overall_impact = analysis.get('overall_impact', {}).get('level', '低')
                if overall_impact in ['高', '中'] or overall_impact == impact_threshold:
                    high_impact_results.append(result)
        
        return {
            'success': True,
            'message': f'成功分析 {len(results)} 条新闻，发现 {len(high_impact_results)} 条高影响新闻',
            'total_news': len(latest_news),
            'important_news': len(important_news),
            'analyzed_count': len(results),
            'high_impact_count': len(high_impact_results),
            'results': high_impact_results,
            'timestamp': datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"自动分析最新新闻错误: {e}")
        raise HTTPException(status_code=500, detail=f"自动分析最新新闻错误: {str(e)}")


# ==================== 批量分析和性能优化API ====================

@app.post("/news/batch-analysis/create")
async def create_batch_analysis_task(
    news_ids: List[int],
    model: str = 'glm',
    priority: str = 'normal'
):
    """创建批量新闻分析任务"""
    try:
        from backend.services.news.batch_analysis_service import get_batch_analysis_service
        from backend.core.data.managers.financial_news_manager import get_financial_news_manager

        if not news_ids:
            raise HTTPException(status_code=400, detail="新闻ID列表不能为空")

        if len(news_ids) > 50:
            raise HTTPException(status_code=400, detail="批量大小不能超过50")

        # 获取新闻数据
        news_manager = get_financial_news_manager()
        news_items = []

        for news_id in news_ids:
            news = news_manager.get_news_by_id(news_id)
            if news:
                news_items.append(news)
            else:
                logger.warning(f"新闻ID不存在: {news_id}")

        if not news_items:
            raise HTTPException(status_code=404, detail="没有找到有效的新闻数据")

        # 创建批量分析任务
        batch_service = get_batch_analysis_service()
        task_id = await batch_service.create_batch_analysis_task(
            news_items=news_items,
            model=model,
            priority=priority
        )

        return {
            'success': True,
            'task_id': task_id,
            'total_items': len(news_items),
            'model': model,
            'timestamp': datetime.datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"创建批量分析任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建批量分析任务失败: {str(e)}")


@app.post("/news/batch-analysis/{task_id}/start")
async def start_batch_analysis_task(task_id: str):
    """开始执行批量分析任务"""
    try:
        from backend.services.news.batch_analysis_service import get_batch_analysis_service

        batch_service = get_batch_analysis_service()

        # 异步执行批量分析
        asyncio.create_task(batch_service.start_batch_analysis(task_id))

        return {
            'success': True,
            'task_id': task_id,
            'status': 'started',
            'message': '批量分析任务已开始执行',
            'timestamp': datetime.datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"启动批量分析任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"启动批量分析任务失败: {str(e)}")


@app.get("/news/batch-analysis/{task_id}/status")
async def get_batch_analysis_status(task_id: str):
    """获取批量分析任务状态"""
    try:
        from backend.services.news.batch_analysis_service import get_batch_analysis_service

        batch_service = get_batch_analysis_service()
        status = batch_service.get_task_status(task_id)

        if not status:
            raise HTTPException(status_code=404, detail="任务不存在")

        return {
            'success': True,
            'task_status': status,
            'timestamp': datetime.datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"获取批量分析状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取批量分析状态失败: {str(e)}")


@app.get("/cache/stats")
async def get_cache_stats():
    """获取缓存统计信息"""
    try:
        from backend.core.cache.smart_cache_manager import get_smart_cache_manager

        cache_manager = get_smart_cache_manager()
        stats = cache_manager.get_stats()

        return {
            'success': True,
            'cache_stats': stats,
            'timestamp': datetime.datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"获取缓存统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取缓存统计失败: {str(e)}")


@app.post("/cache/clear-expired")
async def clear_expired_cache():
    """清理过期缓存"""
    try:
        from backend.core.cache.smart_cache_manager import get_smart_cache_manager

        cache_manager = get_smart_cache_manager()
        cache_manager.clear_expired()

        return {
            'success': True,
            'message': '过期缓存清理完成',
            'timestamp': datetime.datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"清理过期缓存失败: {e}")
        raise HTTPException(status_code=500, detail=f"清理过期缓存失败: {str(e)}")

# ==================== 增强AI新闻分析API ====================

@app.get("/news/impact-analysis/high-impact")
async def get_high_impact_news(
    hours_back: int = 24,
    min_score: float = 70.0
):
    """获取高影响力新闻"""
    try:
        from backend.services.news.news_impact_analyzer import get_news_impact_analyzer

        analyzer = get_news_impact_analyzer()
        high_impact_news = analyzer.get_high_impact_news(
            hours_back=hours_back,
            min_score=min_score
        )

        return {
            'success': True,
            'count': len(high_impact_news),
            'data': high_impact_news,
            'filters': {
                'hours_back': hours_back,
                'min_score': min_score
            },
            'timestamp': datetime.datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"获取高影响力新闻失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取高影响力新闻失败: {str(e)}")


@app.get("/news/impact-analysis/recent-with-scores")
async def get_recent_analyses_with_scores(
    limit: int = 20,
    impact_level: Optional[str] = None,
    min_score: Optional[float] = None
):
    """获取最近的分析结果（包含评分和标签）"""
    try:
        from backend.services.news.news_impact_analyzer import get_news_impact_analyzer

        analyzer = get_news_impact_analyzer()
        recent_analyses = analyzer.get_recent_analyses(
            limit=limit,
            impact_level=impact_level,
            min_score=min_score
        )

        return {
            'success': True,
            'count': len(recent_analyses),
            'data': recent_analyses,
            'filters': {
                'limit': limit,
                'impact_level': impact_level,
                'min_score': min_score
            },
            'timestamp': datetime.datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"获取最近分析结果失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取最近分析结果失败: {str(e)}")

# ==================== 新增API结束 ====================


# Market overview endpoint
@app.get("/market/overview")
async def get_market_overview(market: Optional[str] = None):
    """获取市场概览"""
    try:
        # Mock market overview data - in a real implementation, this would call akshare
        overview_data = {
            'indices': {
                'shanghai_composite': {
                    'name': '上证指数',
                    'current': 3245.67,
                    'change': -12.34,
                    'change_percent': -0.38
                },
                'shenzhen_component': {
                    'name': '深证成指',
                    'current': 12456.78,
                    'change': 23.45,
                    'change_percent': 0.19
                },
                'nasdaq': {
                    'name': '纳斯达克',
                    'current': 14567.89,
                    'change': 45.67,
                    'change_percent': 0.31
                },
                'sp500': {
                    'name': '标普500',
                    'current': 4234.56,
                    'change': -8.90,
                    'change_percent': -0.21
                }
            },
            'market_status': {
                'shanghai': {'status': 'closed', 'next_open': '2024-01-08 09:30:00'},
                'shenzhen': {'status': 'closed', 'next_open': '2024-01-08 09:30:00'},
                'nasdaq': {'status': 'closed', 'next_open': '2024-01-08 09:30:00'},
                'nyse': {'status': 'closed', 'next_open': '2024-01-08 09:30:00'}
            },
            'top_gainers': [
                {'symbol': 'AAPL', 'name': 'Apple Inc.', 'change_percent': 3.45, 'price': 185.23},
                {'symbol': '600519.SH', 'name': '贵州茅台', 'change_percent': 2.89, 'price': 1678.90},
                {'symbol': 'TSLA', 'name': 'Tesla Inc.', 'change_percent': 2.67, 'price': 245.67}
            ],
            'top_losers': [
                {'symbol': 'META', 'name': 'Meta Platforms', 'change_percent': -2.34, 'price': 356.78},
                {'symbol': '000002.SZ', 'name': '万科A', 'change_percent': -1.98, 'price': 12.34},
                {'symbol': 'NFLX', 'name': 'Netflix Inc.', 'change_percent': -1.76, 'price': 567.89}
            ]
        }
        
        return {
            'success': True,
            'market': market or 'global',
            'data': overview_data,
            'timestamp': datetime.datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取市场概览错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取市场概览错误: {str(e)}")


# Market status endpoint
@app.get("/market/status")
async def get_market_status(market: Optional[str] = None):
    """获取市场状态"""
    try:
        # Mock market status data
        status_data = {
            'shanghai': {'status': 'closed', 'next_open': '2024-01-08 09:30:00'},
            'shenzhen': {'status': 'closed', 'next_open': '2024-01-08 09:30:00'},
            'nasdaq': {'status': 'closed', 'next_open': '2024-01-08 09:30:00'},
            'nyse': {'status': 'closed', 'next_open': '2024-01-08 09:30:00'},
            'hkex': {'status': 'closed', 'next_open': '2024-01-08 09:30:00'}
        }
        
        if market and market in status_data:
            return {
                'success': True,
                'market': market,
                'status': status_data[market],
                'timestamp': datetime.datetime.now().isoformat()
            }
        
        return {
            'success': True,
            'markets': status_data,
            'timestamp': datetime.datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取市场状态错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取市场状态错误: {str(e)}")


# Watchlist endpoints
class WatchlistRequest(BaseModel):
    action: str  # 'add', 'remove', 'update', 'list'
    symbol: Optional[str] = None
    target_price: Optional[float] = None
    stop_loss: Optional[float] = None
    alerts_enabled: Optional[bool] = True


# Simple in-memory watchlist storage (in production, use a database)
watchlist_storage = {}


@app.post("/watchlist/manage")
async def manage_watchlist(request: WatchlistRequest):
    """管理观察列表"""
    try:
        action = request.action.lower()
        
        if action == 'list':
            # Return all watchlist items
            watchlist_items = []
            for symbol, data in watchlist_storage.items():
                watchlist_items.append({
                    'symbol': symbol,
                    'added_date': data.get('added_date'),
                    'target_price': data.get('target_price'),
                    'stop_loss': data.get('stop_loss'),
                    'alerts_enabled': data.get('alerts_enabled', True),
                    'current_price': data.get('current_price', 0.0),
                    'change_percent': data.get('change_percent', 0.0)
                })
            
            return {
                'success': True,
                'action': 'list',
                'watchlist': watchlist_items,
                'count': len(watchlist_items),
                'timestamp': datetime.datetime.now().isoformat()
            }
        
        elif action == 'add':
            if not request.symbol:
                raise HTTPException(status_code=400, detail="添加到观察列表需要提供股票代码")
            
            symbol = request.symbol.upper()
            watchlist_storage[symbol] = {
                'added_date': datetime.datetime.now().isoformat(),
                'target_price': request.target_price,
                'stop_loss': request.stop_loss,
                'alerts_enabled': request.alerts_enabled,
                'current_price': 0.0,  # Would be fetched from real data source
                'change_percent': 0.0
            }
            
            return {
                'success': True,
                'action': 'add',
                'symbol': symbol,
                'message': f'{symbol} 已添加到观察列表',
                'timestamp': datetime.datetime.now().isoformat()
            }
        
        elif action == 'remove':
            if not request.symbol:
                raise HTTPException(status_code=400, detail="移除观察列表需要提供股票代码")
            
            symbol = request.symbol.upper()
            if symbol in watchlist_storage:
                del watchlist_storage[symbol]
                return {
                    'success': True,
                    'action': 'remove',
                    'symbol': symbol,
                    'message': f'{symbol} 已从观察列表移除',
                    'timestamp': datetime.datetime.now().isoformat()
                }
            else:
                raise HTTPException(status_code=404, detail=f'{symbol} 不在观察列表中')
        
        elif action == 'update':
            if not request.symbol:
                raise HTTPException(status_code=400, detail="更新观察列表需要提供股票代码")
            
            symbol = request.symbol.upper()
            if symbol in watchlist_storage:
                if request.target_price is not None:
                    watchlist_storage[symbol]['target_price'] = request.target_price
                if request.stop_loss is not None:
                    watchlist_storage[symbol]['stop_loss'] = request.stop_loss
                if request.alerts_enabled is not None:
                    watchlist_storage[symbol]['alerts_enabled'] = request.alerts_enabled
                
                return {
                    'success': True,
                    'action': 'update',
                    'symbol': symbol,
                    'message': f'{symbol} 观察列表信息已更新',
                    'timestamp': datetime.datetime.now().isoformat()
                }
            else:
                raise HTTPException(status_code=404, detail=f'{symbol} 不在观察列表中')
        
        else:
            raise HTTPException(status_code=400, detail=f"不支持的操作: {action}")
        
    except Exception as e:
        logger.error(f"管理观察列表错误: {e}")
        raise HTTPException(status_code=500, detail=f"管理观察列表错误: {str(e)}")


# Data export endpoint
class DataExportRequest(BaseModel):
    type: str  # 'stock-data', 'news', 'technical-indicators'
    symbol: str
    format: str  # 'csv', 'json'
    period: Optional[str] = '1y'
    indicators: Optional[List[str]] = None


@app.post("/data/export")
async def export_data(request: DataExportRequest):
    """导出数据"""
    try:
        # Mock data export - in production, this would generate actual files
        export_data = {
            'symbol': request.symbol,
            'type': request.type,
            'format': request.format,
            'period': request.period,
            'export_time': datetime.datetime.now().isoformat(),
            'data': f"Mock {request.type} data for {request.symbol} in {request.format} format"
        }
        
        if request.format == 'json':
            import json
            content = json.dumps(export_data, ensure_ascii=False, indent=2)
            media_type = 'application/json'
            filename = f"{request.symbol}_{request.type}.json"
        else:  # CSV
            content = f"Symbol,Type,Data\n{request.symbol},{request.type},Mock data"
            media_type = 'text/csv'
            filename = f"{request.symbol}_{request.type}.csv"
        
        from fastapi.responses import Response
        return Response(
            content=content,
            media_type=media_type,
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
        
    except Exception as e:
        logger.error(f"导出数据错误: {e}")
        raise HTTPException(status_code=500, detail=f"导出数据错误: {str(e)}")


# === 缓存管理端点 ===

@app.get("/cache/stats")
async def get_cache_stats():
    """获取缓存统计信息"""
    try:
        stats = cache_manager.get_global_stats()
        return {
            "success": True,
            "stats": stats,
            "timestamp": datetime.datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取缓存统计错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取缓存统计错误: {str(e)}")

@app.post("/cache/clear")
async def clear_cache():
    """清空所有缓存"""
    try:
        cache_manager.clear_all()
        return {
            "success": True,
            "message": "所有缓存已清空",
            "timestamp": datetime.datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"清空缓存错误: {e}")
        raise HTTPException(status_code=500, detail=f"清空缓存错误: {str(e)}")

@app.delete("/cache/{cache_type}")
async def clear_cache_by_type(cache_type: str):
    """清空指定类型的缓存"""
    try:
        cache = cache_manager.get_cache(cache_type)
        if cache:
            cache.clear()
            return {
                "success": True,
                "message": f"{cache_type} 缓存已清空",
                "cache_type": cache_type,
                "timestamp": datetime.datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=404, detail=f"缓存类型 {cache_type} 不存在")
    except Exception as e:
        logger.error(f"清空 {cache_type} 缓存错误: {e}")
        raise HTTPException(status_code=500, detail=f"清空缓存错误: {str(e)}")

@app.get("/cache/health")
async def cache_health_check():
    """缓存系统健康检查"""
    try:
        stats = cache_manager.get_global_stats()
        total_size = sum(stat['size'] for stat in stats.values())
        total_max_size = sum(stat['max_size'] for stat in stats.values())
        avg_hit_rate = sum(stat['hit_rate'] for stat in stats.values()) / len(stats) if stats else 0
        
        health_status = {
            "status": "healthy",
            "total_cached_items": total_size,
            "total_capacity": total_max_size,
            "utilization_rate": round(total_size / total_max_size * 100, 2) if total_max_size > 0 else 0,
            "average_hit_rate": round(avg_hit_rate, 2),
            "cache_types": list(stats.keys()),
            "timestamp": datetime.datetime.now().isoformat()
        }
        
        # 判断健康状态
        if avg_hit_rate < 30:
            health_status["status"] = "warning"
            health_status["warning"] = "缓存命中率较低"
        elif total_size / total_max_size > 0.9:
            health_status["status"] = "warning"  
            health_status["warning"] = "缓存使用率较高"
        
        return health_status
    except Exception as e:
        logger.error(f"缓存健康检查错误: {e}")
        raise HTTPException(status_code=500, detail=f"缓存健康检查错误: {str(e)}")


# === 用户聊天记录管理接口 ===

@app.delete("/chat/user-memory")
async def clear_user_chat_memory(current_user: dict = Depends(get_current_active_user)):
    """清除当前用户的聊天记录和内存"""
    try:
        user_id = current_user['id']
        
        # 清除后端AI工作流内存
        try:
            from backend.ai.user_aware_workflow import UserAwareAIWorkflowManager
            # 这里可以添加清除用户内存的逻辑
            logger.info(f"已清除用户 {user_id} 的AI工作流内存")
        except ImportError:
            logger.warning("用户感知工作流管理器不可用")
        
        return {
            "message": f"用户 {user_id} 的聊天记录已清除",
            "user_id": user_id,
            "timestamp": datetime.datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"清除用户聊天记录失败: {e}")
        raise HTTPException(status_code=500, detail=f"清除聊天记录失败: {str(e)}")


@app.get("/chat/user-memory/stats")
async def get_user_chat_memory_stats(current_user: dict = Depends(get_current_active_user)):
    """获取当前用户的聊天记录统计"""
    try:
        user_id = current_user['id']
        
        # 获取用户内存统计
        stats = {
            "user_id": user_id,
            "username": current_user.get('username', 'unknown'),
            "has_chat_memory": False,
            "timestamp": datetime.datetime.now().isoformat()
        }
        
        try:
            from backend.ai.user_aware_workflow import UserAwareAIWorkflowManager
            # 这里可以添加获取用户内存统计的逻辑
            stats["has_chat_memory"] = True
        except ImportError:
            logger.warning("用户感知工作流管理器不可用")
        
        return stats
    except Exception as e:
        logger.error(f"获取用户聊天记录统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计失败: {str(e)}")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="启动AI金融分析后端服务器")
    parser.add_argument("--host", default="127.0.0.1", help="服务器主机地址")
    parser.add_argument("--port", type=int, default=8000, help="服务器端口")
    parser.add_argument("--reload", action="store_true", help="开启自动重载")
    
    args = parser.parse_args()
    
    logger.info(f"启动AI金融分析后端服务器...")
    logger.info(f"服务地址: http://{args.host}:{args.port}")
    logger.info(f"API文档: http://{args.host}:{args.port}/docs")
    
    uvicorn.run(
        "backend.server:app",
        host=args.host,
        port=args.port,
        reload=args.reload,
        log_level="info"
    )

