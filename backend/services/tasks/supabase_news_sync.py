#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Supabase新闻自动化同步任务模块
负责从AkShare获取股票新闻数据并存储到Supabase数据库
"""

import os
import logging
import asyncio
import time
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any, List, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed

import akshare as ak
import pandas as pd
from dotenv import load_dotenv

try:
    from supabase import create_client, Client
    SUPABASE_AVAILABLE = True
except ImportError:
    SUPABASE_AVAILABLE = False
    Client = None

from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import CronTrigger
from apscheduler.jobstores.memory import MemoryJobStore
from apscheduler.executors.asyncio import AsyncIOExecutor

# 加载环境变量
load_dotenv()

logger = logging.getLogger(__name__)


class SupabaseNewsHandler:
    """Supabase新闻数据处理器"""
    
    def __init__(self):
        """初始化Supabase客户端"""
        if not SUPABASE_AVAILABLE:
            raise ImportError("Supabase包未安装，请运行: pip install supabase>=1.0.0")
        
        self.supabase_url = os.getenv('SUPABASE_URL')
        self.supabase_key = os.getenv('SUPABASE_KEY')
        
        if not self.supabase_url or not self.supabase_key:
            raise ValueError("缺少Supabase配置：请在.env文件中设置SUPABASE_URL和SUPABASE_KEY")
        
        try:
            self.supabase: Client = create_client(self.supabase_url, self.supabase_key)
            logger.info("Supabase客户端初始化成功")
        except Exception as e:
            logger.error(f"Supabase客户端初始化失败: {e}")
            raise
    
    def _standardize_data(self, df: pd.DataFrame, source_name: str) -> List[Dict[str, Any]]:
        """
        将从akshare获取的DataFrame标准化为Supabase表格式
        
        Args:
            df: 原始数据DataFrame
            source_name: 数据源名称
            
        Returns:
            标准化的新闻数据列表
        """
        if df.empty:
            logger.warning(f"{source_name}: 获取到的数据为空")
            return []
        
        standardized_news = []
        
        try:
            for _, row in df.iterrows():
                news_item = {}
                
                # 根据不同数据源的字段映射进行标准化
                if source_name == '东方财富':
                    # stock_news_em 的字段映射
                    news_item = {
                        'title': str(row.get('标题', '')).strip(),
                        'content': str(row.get('内容', ''))[:1000],  # 限制内容长度
                        'published_at': self._parse_datetime(str(row.get('发布时间', ''))),
                        'url': str(row.get('新闻链接', '')).strip(),
                        'source': source_name,
                        'related_stocks': self._extract_stock_codes(str(row.get('相关股票', '')))
                    }
                elif source_name == '财新网':
                    # stock_news_main_cx 的字段映射 (实际字段: tag, summary, interval_time, pub_time, url)
                    news_item = {
                        'title': str(row.get('tag', '')).strip(),
                        'content': str(row.get('summary', ''))[:1000],
                        'published_at': self._parse_datetime(str(row.get('pub_time', ''))),
                        'url': str(row.get('url', '')).strip(),
                        'source': source_name,
                        'related_stocks': []  # 财新网没有直接的股票代码字段
                    }
                
                # 验证必要字段
                if (news_item.get('title') and 
                    news_item.get('published_at') and 
                    len(news_item['title']) > 5):  # 确保标题有意义
                    
                    # 添加更新时间
                    news_item['updated_at'] = datetime.now().isoformat()
                    
                    standardized_news.append(news_item)
                else:
                    logger.debug(f"跳过无效新闻数据: {news_item.get('title', '')[:50]}")
                    
        except Exception as e:
            logger.error(f"标准化{source_name}数据时发生错误: {e}")
        
        logger.info(f"{source_name}: 成功标准化 {len(standardized_news)} 条新闻")
        return standardized_news
    
    def _parse_datetime(self, time_str: str) -> Optional[str]:
        """
        解析时间字符串为ISO格式
        
        Args:
            time_str: 时间字符串
            
        Returns:
            ISO格式的时间字符串，或None如果解析失败
        """
        try:
            time_str = time_str.strip()
            if not time_str or time_str == 'nan':
                return None
            
            # 尝试不同的时间格式
            formats = [
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%d %H:%M',
                '%Y-%m-%d',
                '%m-%d %H:%M',
                '%Y年%m月%d日 %H:%M:%S',
                '%Y年%m月%d日'
            ]
            
            for fmt in formats:
                try:
                    if '%Y' not in fmt and '年' not in fmt:
                        # 对于没有年份的格式，假设是当前年份
                        time_str = f"{datetime.now().year}-{time_str}"
                        fmt = f"%Y-{fmt}"
                    
                    dt = datetime.strptime(time_str, fmt)
                    return dt.isoformat()
                except ValueError:
                    continue
            
            logger.warning(f"无法解析时间格式: {time_str}")
            return None
            
        except Exception as e:
            logger.error(f"时间解析错误: {e}")
            return None
    
    def _extract_stock_codes(self, stock_text: str) -> List[str]:
        """
        从文本中提取股票代码
        
        Args:
            stock_text: 包含股票信息的文本
            
        Returns:
            股票代码列表
        """
        import re
        
        if not stock_text or stock_text == 'nan':
            return []
        
        # 匹配股票代码格式：6位数字或字母+数字组合
        stock_codes = re.findall(r'\b[A-Z]*\d{6}\b|\b[A-Z]{1,4}\d{1,4}\b', stock_text.upper())
        return list(set(stock_codes))  # 去重
    
    def _fetch_from_source(self, fetch_func, source_name: str) -> Dict[str, Any]:
        """
        从单个数据源获取新闻数据
        
        Args:
            fetch_func: akshare获取函数
            source_name: 数据源名称
            
        Returns:
            包含新闻数据和状态信息的字典
        """
        result = {
            'source': source_name,
            'status': 'error',
            'news_data': [],
            'count': 0,
            'error_message': None,
            'response_time': 0
        }
        
        start_time = time.time()
        
        try:
            logger.info(f"开始获取{source_name}新闻数据...")
            
            # 调用akshare接口
            data = fetch_func()
            
            end_time = time.time()
            result['response_time'] = end_time - start_time
            
            if isinstance(data, pd.DataFrame) and not data.empty:
                # 标准化数据格式
                standardized_news = self._standardize_data(data, source_name)
                
                if standardized_news:
                    result['status'] = 'success'
                    result['news_data'] = standardized_news
                    result['count'] = len(standardized_news)
                    logger.info(f"{source_name}获取成功: {len(standardized_news)}条新闻，耗时{result['response_time']:.2f}秒")
                else:
                    result['error_message'] = "数据标准化后为空"
                    logger.warning(f"{source_name}数据标准化后为空")
            else:
                result['error_message'] = "返回数据为空或格式错误"
                logger.warning(f"{source_name}返回数据为空")
                
        except Exception as e:
            end_time = time.time()
            result['response_time'] = end_time - start_time
            result['error_message'] = str(e)
            logger.error(f"{source_name}获取失败: {e}")
        
        return result
    
    async def sync_news(self) -> Dict[str, Any]:
        """
        同步所有来源的新闻数据到Supabase
        
        Returns:
            同步结果统计
        """
        sync_start_time = datetime.now()
        result = {
            'status': 'success',
            'start_time': sync_start_time.isoformat(),
            'sources': {},
            'total_fetched': 0,
            'total_inserted': 0,
            'total_updated': 0,
            'errors': []
        }
        
        try:
            logger.info("开始新闻数据同步任务...")
            
            # 定义数据源
            sources = [
                (ak.stock_news_em, '东方财富'),
                (ak.stock_news_main_cx, '财新网')
            ]
            
            # 并发获取数据
            with ThreadPoolExecutor(max_workers=2) as executor:
                futures = [
                    executor.submit(self._fetch_from_source, func, name)
                    for func, name in sources
                ]
                
                all_news_data = []
                for future in as_completed(futures):
                    try:
                        source_result = future.result()
                        result['sources'][source_result['source']] = source_result
                        
                        if source_result['status'] == 'success':
                            all_news_data.extend(source_result['news_data'])
                            result['total_fetched'] += source_result['count']
                        else:
                            result['errors'].append(f"{source_result['source']}: {source_result['error_message']}")
                            
                    except Exception as e:
                        error_msg = f"处理数据源时发生错误: {e}"
                        result['errors'].append(error_msg)
                        logger.error(error_msg)
            
            # 批量插入/更新到Supabase
            if all_news_data:
                try:
                    logger.info(f"开始将{len(all_news_data)}条新闻数据同步到Supabase...")
                    
                    # 使用upsert操作，基于url字段进行去重
                    response = self.supabase.table('news').upsert(
                        all_news_data,
                        on_conflict='url'
                    ).execute()
                    
                    # 统计插入和更新的记录数
                    if response.data:
                        result['total_inserted'] = len([item for item in response.data if 'created_at' in item])
                        result['total_updated'] = len(response.data) - result['total_inserted']
                        
                    logger.info(f"Supabase同步完成: 插入{result['total_inserted']}条，更新{result['total_updated']}条")
                    
                except Exception as e:
                    error_msg = f"Supabase数据库操作失败: {e}"
                    result['errors'].append(error_msg)
                    result['status'] = 'partial_success' if result['total_fetched'] > 0 else 'error'
                    logger.error(error_msg)
            else:
                logger.warning("没有有效的新闻数据需要同步")
                result['status'] = 'no_data'
        
        except Exception as e:
            error_msg = f"新闻同步任务失败: {e}"
            result['errors'].append(error_msg)
            result['status'] = 'error'
            logger.error(error_msg)
        
        finally:
            sync_end_time = datetime.now()
            result['end_time'] = sync_end_time.isoformat()
            result['duration'] = (sync_end_time - sync_start_time).total_seconds()
            
            logger.info(f"新闻同步任务完成: 状态={result['status']}, 耗时={result['duration']:.2f}秒")
        
        return result


# 主程序入口
if __name__ == "__main__":
    async def main():
        """主程序：执行一次新闻同步测试"""
        try:
            # 设置日志级别
            logging.basicConfig(
                level=logging.INFO,
                format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            
            logger.info("开始执行Supabase新闻同步测试...")
            
            # 检查Supabase是否可用
            if not SUPABASE_AVAILABLE:
                logger.error("Supabase包未安装，请运行: pip install supabase>=1.0.0")
                return
            
            # 创建新闻处理器并执行同步
            handler = SupabaseNewsHandler()
            result = await handler.sync_news()
            
            logger.info("同步测试完成！")
            logger.info(f"同步结果: {result}")
            
        except Exception as e:
            logger.error(f"同步测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    # 运行主程序
    asyncio.run(main())