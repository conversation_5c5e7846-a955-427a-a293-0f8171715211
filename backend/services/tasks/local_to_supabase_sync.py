#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地SQLite到Supabase的新闻数据同步服务
负责将本地财经新闻数据定时同步到Supabase云数据库
"""

import os
import logging
import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dotenv import load_dotenv

try:
    from supabase import create_client, Client
    SUPABASE_AVAILABLE = True
except ImportError:
    SUPABASE_AVAILABLE = False
    Client = None

from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import CronTrigger
from apscheduler.jobstores.memory import MemoryJobStore
from apscheduler.executors.asyncio import AsyncIOExecutor

# 导入本地新闻管理器
from backend.core.data.managers.financial_news_manager import get_financial_news_manager

# 加载环境变量
load_dotenv()

logger = logging.getLogger(__name__)


class LocalToSupabaseSyncService:
    """本地SQLite到Supabase的同步服务"""
    
    def __init__(self):
        """初始化同步服务"""
        if not SUPABASE_AVAILABLE:
            logger.warning("Supabase包未安装，同步功能将被禁用")
            self.supabase_enabled = False
            return
        
        self.supabase_url = os.getenv('SUPABASE_URL')
        self.supabase_key = os.getenv('SUPABASE_KEY')
        
        if not self.supabase_url or not self.supabase_key:
            logger.warning("缺少Supabase配置，同步功能将被禁用")
            self.supabase_enabled = False
            return
        
        try:
            self.supabase: Client = create_client(self.supabase_url, self.supabase_key)
            self.supabase_enabled = True
            logger.info("Supabase客户端初始化成功")
        except Exception as e:
            logger.error(f"Supabase客户端初始化失败: {e}")
            self.supabase_enabled = False
        
        # 获取本地新闻管理器
        self.local_news_manager = get_financial_news_manager()
        
        # 初始化调度器
        self.scheduler = None
        self.is_running = False
        self.sync_stats = {
            'last_sync_time': None,
            'last_sync_status': None,
            'total_synced': 0,
            'sync_errors': []
        }
    
    def _convert_local_to_supabase_format(self, local_news: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        将本地新闻数据格式转换为Supabase格式
        
        Args:
            local_news: 本地新闻数据列表
            
        Returns:
            Supabase格式的新闻数据列表
        """
        supabase_news = []
        
        for news in local_news:
            try:
                # 转换字段映射
                supabase_item = {
                    'title': news.get('title', ''),
                    'content': news.get('content', ''),
                    'published_at': news.get('publish_time', ''),
                    'url': news.get('url', ''),
                    'source': news.get('source_name', news.get('source', '')),
                    'related_stocks': [],  # 本地数据暂时没有股票代码字段
                    'updated_at': datetime.now().isoformat()
                }
                
                # 验证必要字段
                if supabase_item['title'] and supabase_item['published_at']:
                    supabase_news.append(supabase_item)
                else:
                    logger.debug(f"跳过无效新闻数据: {news.get('title', '')[:50]}")
                    
            except Exception as e:
                logger.error(f"转换新闻数据格式失败: {e}")
                continue
        
        return supabase_news
    
    async def sync_recent_news(self, hours_back: int = 24) -> Dict[str, Any]:
        """
        同步最近的新闻数据到Supabase
        
        Args:
            hours_back: 同步最近多少小时的数据
            
        Returns:
            同步结果统计
        """
        if not self.supabase_enabled:
            return {
                'status': 'disabled',
                'message': 'Supabase同步功能未启用'
            }
        
        sync_start_time = datetime.now()
        result = {
            'status': 'success',
            'start_time': sync_start_time.isoformat(),
            'hours_back': hours_back,
            'local_count': 0,
            'synced_count': 0,
            'errors': []
        }
        
        try:
            logger.info(f"开始同步最近{hours_back}小时的新闻数据到Supabase...")
            
            # 从本地数据库获取最近的新闻
            start_time = datetime.now() - timedelta(hours=hours_back)
            end_time = datetime.now()
            
            local_news = self.local_news_manager.get_news_by_timerange(
                start_time=start_time,
                end_time=end_time,
                limit=1000  # 限制数量避免一次性同步过多数据
            )
            
            result['local_count'] = len(local_news)
            
            if not local_news:
                logger.info("没有需要同步的新闻数据")
                result['status'] = 'no_data'
                return result
            
            # 转换数据格式
            supabase_news = self._convert_local_to_supabase_format(local_news)
            
            if not supabase_news:
                logger.warning("转换后没有有效的新闻数据")
                result['status'] = 'no_valid_data'
                return result
            
            # 批量同步到Supabase
            try:
                logger.info(f"开始将{len(supabase_news)}条新闻同步到Supabase...")
                
                # 使用upsert操作，基于url字段进行去重
                response = self.supabase.table('news').upsert(
                    supabase_news,
                    on_conflict='url'
                ).execute()
                
                if response.data:
                    result['synced_count'] = len(response.data)
                    logger.info(f"成功同步{result['synced_count']}条新闻到Supabase")
                else:
                    result['errors'].append("Supabase响应数据为空")
                    result['status'] = 'partial_success'
                    
            except Exception as e:
                error_msg = f"Supabase同步操作失败: {e}"
                result['errors'].append(error_msg)
                result['status'] = 'error'
                logger.error(error_msg)
                
        except Exception as e:
            error_msg = f"同步过程发生错误: {e}"
            result['errors'].append(error_msg)
            result['status'] = 'error'
            logger.error(error_msg)
        
        finally:
            sync_end_time = datetime.now()
            result['end_time'] = sync_end_time.isoformat()
            result['duration'] = (sync_end_time - sync_start_time).total_seconds()
            
            # 更新统计信息
            self.sync_stats['last_sync_time'] = sync_end_time.isoformat()
            self.sync_stats['last_sync_status'] = result['status']
            if result['status'] == 'success':
                self.sync_stats['total_synced'] += result.get('synced_count', 0)
            if result['errors']:
                self.sync_stats['sync_errors'].extend(result['errors'])
                # 只保留最近10个错误
                self.sync_stats['sync_errors'] = self.sync_stats['sync_errors'][-10:]
            
            logger.info(f"新闻同步完成: 状态={result['status']}, 耗时={result['duration']:.2f}秒")
        
        return result
    
    def setup_scheduler(self):
        """设置定时任务调度器"""
        if self.scheduler is not None:
            return
        
        # 配置调度器
        jobstores = {
            'default': MemoryJobStore()
        }
        executors = {
            'default': AsyncIOExecutor()
        }
        job_defaults = {
            'coalesce': False,
            'max_instances': 1,
            'misfire_grace_time': 300  # 5分钟容错时间
        }
        
        self.scheduler = AsyncIOScheduler(
            jobstores=jobstores,
            executors=executors,
            job_defaults=job_defaults,
            timezone='Asia/Shanghai'
        )
        
        # 添加定时同步任务 - 每30分钟执行一次
        self.scheduler.add_job(
            self.sync_recent_news,
            trigger=IntervalTrigger(minutes=30),
            id='sync_to_supabase',
            name='同步本地新闻到Supabase',
            replace_existing=True,
            kwargs={'hours_back': 2}  # 每次同步最近2小时的数据
        )
        
        logger.info("Supabase同步调度器设置完成")
    
    async def start_scheduler(self):
        """启动调度器"""
        if not self.supabase_enabled:
            logger.info("Supabase同步功能未启用，跳过调度器启动")
            return
        
        try:
            if not self.is_running:
                self.setup_scheduler()
                self.scheduler.start()
                self.is_running = True
                logger.info("Supabase同步调度器启动成功")
                
                # 启动时立即执行一次同步
                await self.sync_recent_news(hours_back=24)
            else:
                logger.warning("调度器已经在运行中")
                
        except Exception as e:
            logger.error(f"启动调度器失败: {e}")
            raise
    
    async def stop_scheduler(self):
        """停止调度器"""
        try:
            if self.is_running and self.scheduler:
                self.scheduler.shutdown(wait=True)
                self.is_running = False
                logger.info("Supabase同步调度器已停止")
            else:
                logger.warning("调度器未在运行")
                
        except Exception as e:
            logger.error(f"停止调度器失败: {e}")
            raise
    
    def get_sync_status(self) -> Dict[str, Any]:
        """获取同步状态信息"""
        return {
            'enabled': self.supabase_enabled,
            'running': self.is_running,
            'stats': self.sync_stats.copy()
        }


# 全局实例
_sync_service = None

def get_supabase_sync_service() -> LocalToSupabaseSyncService:
    """获取Supabase同步服务实例（单例模式）"""
    global _sync_service
    if _sync_service is None:
        _sync_service = LocalToSupabaseSyncService()
    return _sync_service


# 主程序入口
if __name__ == "__main__":
    async def main():
        """主程序：测试同步功能"""
        try:
            # 设置日志级别
            logging.basicConfig(
                level=logging.INFO,
                format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            
            logger.info("开始测试本地到Supabase的同步功能...")
            
            # 创建同步服务并测试
            sync_service = get_supabase_sync_service()
            
            if sync_service.supabase_enabled:
                result = await sync_service.sync_recent_news(hours_back=24)
                logger.info(f"同步测试完成: {result}")
            else:
                logger.info("Supabase同步功能未启用")
            
        except Exception as e:
            logger.error(f"同步测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    # 运行主程序
    asyncio.run(main())
