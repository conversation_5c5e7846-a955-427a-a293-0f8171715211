# ================================
# 数据查询中心 - 环境配置文件
# ================================

# ================================
# 服务配置
# ================================
# 应用端口配置
APP_PORT=80
BACKEND_PORT=8000

# 应用环境 (development/production)
APP_ENV=production

# 日志级别 (DEBUG/INFO/WARNING/ERROR)
LOG_LEVEL=INFO

# ================================
# 数据库配置
# ================================
# SQLite数据库文件路径 (容器内路径，通过卷挂载)
DATABASE_PATH=/app/data
FINANCIAL_DB_PATH=/app/data/financial_data.db
NEWS_DB_PATH=/app/data/financial_news.db
USERS_DB_PATH=/app/data/users.db
NEWS_IMPACT_DB_PATH=/app/data/news_impact_analysis.db
DIVERGENCE_DB_PATH=/app/data/divergence_data.db

# ================================
# 大语言模型API配置
# ================================
# 智谱GLM API (主要用于新闻影响分析)
GLM_API_KEY=your_glm_api_key_here

# OpenAI API (可选)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-3.5-turbo

# Google Gemini API (可选)
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_MODEL=gemini-2.0-flash

# SiliconFlow API (可选，国内大模型服务)
SILICONFLOW_API_KEY=your_siliconflow_api_key_here

# ================================
# 金融数据源API配置
# ================================
# Tushare Pro API (股票数据)
TUSHARE_TOKEN=your_tushare_token_here

# Alpha Vantage API (美股数据)
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key_here

# ================================
# Supabase数据库配置
# ================================
# Supabase项目URL
SUPABASE_URL=your_supabase_project_url_here

# Supabase服务角色密钥 (用于服务端操作)
SUPABASE_KEY=your_supabase_service_role_key_here

# ================================
# 搜索引擎API配置
# ================================
# Tavily搜索API (智能搜索)
TAVILY_API_KEY=your_tavily_api_key_here

# Brave搜索API (可选)
BRAVE_SEARCH_API_KEY=your_brave_search_key_here

# Jina AI API (网页解析，可选)
JINA_API_KEY=your_jina_api_key_here

# ================================
# 安全配置
# ================================
# JWT密钥 (用于用户认证)
SECRET_KEY=your-secret-key-change-this-in-production

# JWT Token过期时间 (分钟)
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 密码加密配置
BCRYPT_ROUNDS=12

# ================================
# 应用功能配置
# ================================
# AI工作流配置
AGENT_RECURSION_LIMIT=5
MAX_PLAN_ITERATIONS=3
MAX_STEP_NUM=10

# 缓存配置
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# 新闻同步配置
NEWS_SYNC_INTERVAL=3600  # 秒
NEWS_MAX_WORKERS=3

# 搜索引擎选择 (tavily/brave)
SEARCH_API=tavily

# ================================
# 开发调试配置
# ================================
# 是否启用调试模式
DEBUG=false

# 是否启用API文档
ENABLE_DOCS=true

# 前端开发服务器地址 (仅开发环境)
FRONTEND_URL=http://localhost:3000

# ================================
# 性能配置
# ================================
# Uvicorn工作进程数
WORKERS=1

# 最大并发连接数
MAX_CONNECTIONS=1000

# 请求超时时间 (秒)
REQUEST_TIMEOUT=300

# ================================
# 监控配置
# ================================
# 健康检查间隔 (秒)
HEALTH_CHECK_INTERVAL=30

# 日志保留天数
LOG_RETENTION_DAYS=30

# ================================
# 注意事项
# ================================
# 1. 请将此文件复制为 .env 并填入实际的API密钥
# 2. 生产环境中务必更改所有默认密钥和密码
# 3. 确保 .env 文件不被提交到版本控制系统
# 4. API密钥获取地址:
#    - GLM: https://open.bigmodel.cn/
#    - OpenAI: https://platform.openai.com/
#    - Tushare: https://tushare.pro/
#    - Alpha Vantage: https://www.alphavantage.co/
#    - Tavily: https://tavily.com/ 